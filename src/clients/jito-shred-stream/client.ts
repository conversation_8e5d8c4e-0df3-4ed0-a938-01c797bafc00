import type { Metada<PERSON> } from '@grpc/grpc-js'
import { type CreateGrpcClientOptions, call, createGrpcClient } from '../../utils'
import { type Heartbeat, type HeartbeatResponse, ShredstreamClient, ShredstreamProxyClient } from './generated/shredstream'
import { JitoShredStreamStreamWrapper, type JitoShredStreamStreamWrapperOptions } from './utils'

export type JitoShredStreamClientOptions = CreateGrpcClientOptions

export class JitoShredStreamClient {
    public readonly grpc: ShredstreamClient
    public readonly proxyGrpc: ShredstreamProxyClient

    public constructor(url: string, protected readonly options: JitoShredStreamClientOptions = {}) {
        this.grpc = createGrpcClient(ShredstreamClient, url, options)
        this.proxyGrpc = createGrpcClient(ShredstreamProxyClient, url, options)
    }

    public createStream(options?: JitoShredStreamStreamWrapperOptions) {
        return new JitoShredStreamStreamWrapper(this.proxyGrpc, options)
    }

    public async sendHeartbeat(request: Heartbeat, metadata?: Metadata) {
        return call<Heartbeat, HeartbeatResponse>(this.grpc.sendHeartbeat.bind(this.grpc), request, metadata)
    }
}
