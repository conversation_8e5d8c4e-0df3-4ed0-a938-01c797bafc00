// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: bundle.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Packet } from "./packet";
import { Header } from "./shared";

export enum DroppedReason {
  BlockhashExpired = 0,
  /** PartiallyProcessed - One or more transactions in the bundle landed on-chain, invalidating the bundle. */
  PartiallyProcessed = 1,
  /** NotFinalized - This indicates bundle was processed but not finalized. This could occur during forks. */
  NotFinalized = 2,
  UNRECOGNIZED = -1,
}

export function droppedReasonFromJSON(object: any): DroppedReason {
  switch (object) {
    case 0:
    case "BlockhashExpired":
      return DroppedReason.BlockhashExpired;
    case 1:
    case "PartiallyProcessed":
      return DroppedReason.PartiallyProcessed;
    case 2:
    case "NotFinalized":
      return DroppedReason.NotFinalized;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DroppedReason.UNRECOGNIZED;
  }
}

export function droppedReasonToJSON(object: DroppedReason): string {
  switch (object) {
    case DroppedReason.BlockhashExpired:
      return "BlockhashExpired";
    case DroppedReason.PartiallyProcessed:
      return "PartiallyProcessed";
    case DroppedReason.NotFinalized:
      return "NotFinalized";
    case DroppedReason.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface Bundle {
  header: Header | undefined;
  packets: Packet[];
}

export interface BundleUuid {
  bundle: Bundle | undefined;
  uuid: string;
}

/**
 * Indicates the bundle was accepted and forwarded to a validator.
 * NOTE: A single bundle may have multiple events emitted if forwarded to many validators.
 */
export interface Accepted {
  /** Slot at which bundle was forwarded. */
  slot: bigint;
  /** Validator identity bundle was forwarded to. */
  validatorIdentity: string;
}

/** Indicates the bundle was dropped and therefore not forwarded to any validator. */
export interface Rejected {
  stateAuctionBidRejected?: StateAuctionBidRejected | undefined;
  winningBatchBidRejected?: WinningBatchBidRejected | undefined;
  simulationFailure?: SimulationFailure | undefined;
  internalError?: InternalError | undefined;
  droppedBundle?: DroppedBundle | undefined;
}

/**
 * Indicates the bundle's bid was high enough to win its state auction.
 * However, not high enough relative to other state auction winners and therefore excluded from being forwarded.
 */
export interface WinningBatchBidRejected {
  /** Auction's unique identifier. */
  auctionId: string;
  /** Bundle's simulated bid. */
  simulatedBidLamports: bigint;
  msg?: string | undefined;
}

/** Indicates the bundle's bid was __not__ high enough to be included in its state auction's set of winners. */
export interface StateAuctionBidRejected {
  /** Auction's unique identifier. */
  auctionId: string;
  /** Bundle's simulated bid. */
  simulatedBidLamports: bigint;
  msg?: string | undefined;
}

/** Bundle dropped due to simulation failure. */
export interface SimulationFailure {
  /** Signature of the offending transaction. */
  txSignature: string;
  msg?: string | undefined;
}

/** Bundle dropped due to an internal error. */
export interface InternalError {
  msg: string;
}

/** Bundle dropped (e.g. because no leader upcoming) */
export interface DroppedBundle {
  msg: string;
}

export interface Finalized {
}

export interface Processed {
  validatorIdentity: string;
  slot: bigint;
  /** / Index within the block. */
  bundleIndex: bigint;
}

export interface Dropped {
  reason: DroppedReason;
}

export interface BundleResult {
  /** Bundle's Uuid. */
  bundleId: string;
  /** Indicated accepted by the block-engine and forwarded to a jito-solana validator. */
  accepted?:
    | Accepted
    | undefined;
  /** Rejected by the block-engine. */
  rejected?:
    | Rejected
    | undefined;
  /** Reached finalized commitment level. */
  finalized?:
    | Finalized
    | undefined;
  /** Reached a processed commitment level. */
  processed?:
    | Processed
    | undefined;
  /** Was accepted and forwarded by the block-engine but never landed on-chain. */
  dropped?: Dropped | undefined;
}

function createBaseBundle(): Bundle {
  return { header: undefined, packets: [] };
}

export const Bundle: MessageFns<Bundle> = {
  encode(message: Bundle, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      Header.encode(message.header, writer.uint32(18).fork()).join();
    }
    for (const v of message.packets) {
      Packet.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Bundle {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBundle();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.header = Header.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.packets.push(Packet.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Bundle, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Bundle | Bundle[]> | Iterable<Bundle | Bundle[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Bundle.encode(p).finish()];
        }
      } else {
        yield* [Bundle.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Bundle>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Bundle> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Bundle.decode(p)];
        }
      } else {
        yield* [Bundle.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Bundle {
    return {
      header: isSet(object.header) ? Header.fromJSON(object.header) : undefined,
      packets: globalThis.Array.isArray(object?.packets) ? object.packets.map((e: any) => Packet.fromJSON(e)) : [],
    };
  },

  toJSON(message: Bundle): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = Header.toJSON(message.header);
    }
    if (message.packets?.length) {
      obj.packets = message.packets.map((e) => Packet.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Bundle>, I>>(base?: I): Bundle {
    return Bundle.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Bundle>, I>>(object: I): Bundle {
    const message = createBaseBundle();
    message.header = (object.header !== undefined && object.header !== null)
      ? Header.fromPartial(object.header)
      : undefined;
    message.packets = object.packets?.map((e) => Packet.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBundleUuid(): BundleUuid {
  return { bundle: undefined, uuid: "" };
}

export const BundleUuid: MessageFns<BundleUuid> = {
  encode(message: BundleUuid, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bundle !== undefined) {
      Bundle.encode(message.bundle, writer.uint32(10).fork()).join();
    }
    if (message.uuid !== "") {
      writer.uint32(18).string(message.uuid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BundleUuid {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBundleUuid();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bundle = Bundle.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.uuid = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<BundleUuid, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<BundleUuid | BundleUuid[]> | Iterable<BundleUuid | BundleUuid[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BundleUuid.encode(p).finish()];
        }
      } else {
        yield* [BundleUuid.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, BundleUuid>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<BundleUuid> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BundleUuid.decode(p)];
        }
      } else {
        yield* [BundleUuid.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): BundleUuid {
    return {
      bundle: isSet(object.bundle) ? Bundle.fromJSON(object.bundle) : undefined,
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "",
    };
  },

  toJSON(message: BundleUuid): unknown {
    const obj: any = {};
    if (message.bundle !== undefined) {
      obj.bundle = Bundle.toJSON(message.bundle);
    }
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BundleUuid>, I>>(base?: I): BundleUuid {
    return BundleUuid.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BundleUuid>, I>>(object: I): BundleUuid {
    const message = createBaseBundleUuid();
    message.bundle = (object.bundle !== undefined && object.bundle !== null)
      ? Bundle.fromPartial(object.bundle)
      : undefined;
    message.uuid = object.uuid ?? "";
    return message;
  },
};

function createBaseAccepted(): Accepted {
  return { slot: 0n, validatorIdentity: "" };
}

export const Accepted: MessageFns<Accepted> = {
  encode(message: Accepted, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.slot);
    }
    if (message.validatorIdentity !== "") {
      writer.uint32(18).string(message.validatorIdentity);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Accepted {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAccepted();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.validatorIdentity = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Accepted, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Accepted | Accepted[]> | Iterable<Accepted | Accepted[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Accepted.encode(p).finish()];
        }
      } else {
        yield* [Accepted.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Accepted>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Accepted> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Accepted.decode(p)];
        }
      } else {
        yield* [Accepted.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Accepted {
    return {
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      validatorIdentity: isSet(object.validatorIdentity) ? globalThis.String(object.validatorIdentity) : "",
    };
  },

  toJSON(message: Accepted): unknown {
    const obj: any = {};
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.validatorIdentity !== "") {
      obj.validatorIdentity = message.validatorIdentity;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Accepted>, I>>(base?: I): Accepted {
    return Accepted.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Accepted>, I>>(object: I): Accepted {
    const message = createBaseAccepted();
    message.slot = object.slot ?? 0n;
    message.validatorIdentity = object.validatorIdentity ?? "";
    return message;
  },
};

function createBaseRejected(): Rejected {
  return {
    stateAuctionBidRejected: undefined,
    winningBatchBidRejected: undefined,
    simulationFailure: undefined,
    internalError: undefined,
    droppedBundle: undefined,
  };
}

export const Rejected: MessageFns<Rejected> = {
  encode(message: Rejected, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.stateAuctionBidRejected !== undefined) {
      StateAuctionBidRejected.encode(message.stateAuctionBidRejected, writer.uint32(10).fork()).join();
    }
    if (message.winningBatchBidRejected !== undefined) {
      WinningBatchBidRejected.encode(message.winningBatchBidRejected, writer.uint32(18).fork()).join();
    }
    if (message.simulationFailure !== undefined) {
      SimulationFailure.encode(message.simulationFailure, writer.uint32(26).fork()).join();
    }
    if (message.internalError !== undefined) {
      InternalError.encode(message.internalError, writer.uint32(34).fork()).join();
    }
    if (message.droppedBundle !== undefined) {
      DroppedBundle.encode(message.droppedBundle, writer.uint32(42).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Rejected {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRejected();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.stateAuctionBidRejected = StateAuctionBidRejected.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.winningBatchBidRejected = WinningBatchBidRejected.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.simulationFailure = SimulationFailure.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.internalError = InternalError.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.droppedBundle = DroppedBundle.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Rejected, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Rejected | Rejected[]> | Iterable<Rejected | Rejected[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Rejected.encode(p).finish()];
        }
      } else {
        yield* [Rejected.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Rejected>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Rejected> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Rejected.decode(p)];
        }
      } else {
        yield* [Rejected.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Rejected {
    return {
      stateAuctionBidRejected: isSet(object.stateAuctionBidRejected)
        ? StateAuctionBidRejected.fromJSON(object.stateAuctionBidRejected)
        : undefined,
      winningBatchBidRejected: isSet(object.winningBatchBidRejected)
        ? WinningBatchBidRejected.fromJSON(object.winningBatchBidRejected)
        : undefined,
      simulationFailure: isSet(object.simulationFailure)
        ? SimulationFailure.fromJSON(object.simulationFailure)
        : undefined,
      internalError: isSet(object.internalError) ? InternalError.fromJSON(object.internalError) : undefined,
      droppedBundle: isSet(object.droppedBundle) ? DroppedBundle.fromJSON(object.droppedBundle) : undefined,
    };
  },

  toJSON(message: Rejected): unknown {
    const obj: any = {};
    if (message.stateAuctionBidRejected !== undefined) {
      obj.stateAuctionBidRejected = StateAuctionBidRejected.toJSON(message.stateAuctionBidRejected);
    }
    if (message.winningBatchBidRejected !== undefined) {
      obj.winningBatchBidRejected = WinningBatchBidRejected.toJSON(message.winningBatchBidRejected);
    }
    if (message.simulationFailure !== undefined) {
      obj.simulationFailure = SimulationFailure.toJSON(message.simulationFailure);
    }
    if (message.internalError !== undefined) {
      obj.internalError = InternalError.toJSON(message.internalError);
    }
    if (message.droppedBundle !== undefined) {
      obj.droppedBundle = DroppedBundle.toJSON(message.droppedBundle);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Rejected>, I>>(base?: I): Rejected {
    return Rejected.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Rejected>, I>>(object: I): Rejected {
    const message = createBaseRejected();
    message.stateAuctionBidRejected =
      (object.stateAuctionBidRejected !== undefined && object.stateAuctionBidRejected !== null)
        ? StateAuctionBidRejected.fromPartial(object.stateAuctionBidRejected)
        : undefined;
    message.winningBatchBidRejected =
      (object.winningBatchBidRejected !== undefined && object.winningBatchBidRejected !== null)
        ? WinningBatchBidRejected.fromPartial(object.winningBatchBidRejected)
        : undefined;
    message.simulationFailure = (object.simulationFailure !== undefined && object.simulationFailure !== null)
      ? SimulationFailure.fromPartial(object.simulationFailure)
      : undefined;
    message.internalError = (object.internalError !== undefined && object.internalError !== null)
      ? InternalError.fromPartial(object.internalError)
      : undefined;
    message.droppedBundle = (object.droppedBundle !== undefined && object.droppedBundle !== null)
      ? DroppedBundle.fromPartial(object.droppedBundle)
      : undefined;
    return message;
  },
};

function createBaseWinningBatchBidRejected(): WinningBatchBidRejected {
  return { auctionId: "", simulatedBidLamports: 0n, msg: undefined };
}

export const WinningBatchBidRejected: MessageFns<WinningBatchBidRejected> = {
  encode(message: WinningBatchBidRejected, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.auctionId !== "") {
      writer.uint32(10).string(message.auctionId);
    }
    if (message.simulatedBidLamports !== 0n) {
      if (BigInt.asUintN(64, message.simulatedBidLamports) !== message.simulatedBidLamports) {
        throw new globalThis.Error("value provided for field message.simulatedBidLamports of type uint64 too large");
      }
      writer.uint32(16).uint64(message.simulatedBidLamports);
    }
    if (message.msg !== undefined) {
      writer.uint32(26).string(message.msg);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): WinningBatchBidRejected {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWinningBatchBidRejected();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.auctionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.simulatedBidLamports = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.msg = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<WinningBatchBidRejected, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<WinningBatchBidRejected | WinningBatchBidRejected[]>
      | Iterable<WinningBatchBidRejected | WinningBatchBidRejected[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [WinningBatchBidRejected.encode(p).finish()];
        }
      } else {
        yield* [WinningBatchBidRejected.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, WinningBatchBidRejected>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<WinningBatchBidRejected> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [WinningBatchBidRejected.decode(p)];
        }
      } else {
        yield* [WinningBatchBidRejected.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): WinningBatchBidRejected {
    return {
      auctionId: isSet(object.auctionId) ? globalThis.String(object.auctionId) : "",
      simulatedBidLamports: isSet(object.simulatedBidLamports) ? BigInt(object.simulatedBidLamports) : 0n,
      msg: isSet(object.msg) ? globalThis.String(object.msg) : undefined,
    };
  },

  toJSON(message: WinningBatchBidRejected): unknown {
    const obj: any = {};
    if (message.auctionId !== "") {
      obj.auctionId = message.auctionId;
    }
    if (message.simulatedBidLamports !== 0n) {
      obj.simulatedBidLamports = message.simulatedBidLamports.toString();
    }
    if (message.msg !== undefined) {
      obj.msg = message.msg;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WinningBatchBidRejected>, I>>(base?: I): WinningBatchBidRejected {
    return WinningBatchBidRejected.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WinningBatchBidRejected>, I>>(object: I): WinningBatchBidRejected {
    const message = createBaseWinningBatchBidRejected();
    message.auctionId = object.auctionId ?? "";
    message.simulatedBidLamports = object.simulatedBidLamports ?? 0n;
    message.msg = object.msg ?? undefined;
    return message;
  },
};

function createBaseStateAuctionBidRejected(): StateAuctionBidRejected {
  return { auctionId: "", simulatedBidLamports: 0n, msg: undefined };
}

export const StateAuctionBidRejected: MessageFns<StateAuctionBidRejected> = {
  encode(message: StateAuctionBidRejected, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.auctionId !== "") {
      writer.uint32(10).string(message.auctionId);
    }
    if (message.simulatedBidLamports !== 0n) {
      if (BigInt.asUintN(64, message.simulatedBidLamports) !== message.simulatedBidLamports) {
        throw new globalThis.Error("value provided for field message.simulatedBidLamports of type uint64 too large");
      }
      writer.uint32(16).uint64(message.simulatedBidLamports);
    }
    if (message.msg !== undefined) {
      writer.uint32(26).string(message.msg);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StateAuctionBidRejected {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStateAuctionBidRejected();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.auctionId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.simulatedBidLamports = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.msg = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<StateAuctionBidRejected, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<StateAuctionBidRejected | StateAuctionBidRejected[]>
      | Iterable<StateAuctionBidRejected | StateAuctionBidRejected[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [StateAuctionBidRejected.encode(p).finish()];
        }
      } else {
        yield* [StateAuctionBidRejected.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, StateAuctionBidRejected>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<StateAuctionBidRejected> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [StateAuctionBidRejected.decode(p)];
        }
      } else {
        yield* [StateAuctionBidRejected.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): StateAuctionBidRejected {
    return {
      auctionId: isSet(object.auctionId) ? globalThis.String(object.auctionId) : "",
      simulatedBidLamports: isSet(object.simulatedBidLamports) ? BigInt(object.simulatedBidLamports) : 0n,
      msg: isSet(object.msg) ? globalThis.String(object.msg) : undefined,
    };
  },

  toJSON(message: StateAuctionBidRejected): unknown {
    const obj: any = {};
    if (message.auctionId !== "") {
      obj.auctionId = message.auctionId;
    }
    if (message.simulatedBidLamports !== 0n) {
      obj.simulatedBidLamports = message.simulatedBidLamports.toString();
    }
    if (message.msg !== undefined) {
      obj.msg = message.msg;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StateAuctionBidRejected>, I>>(base?: I): StateAuctionBidRejected {
    return StateAuctionBidRejected.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StateAuctionBidRejected>, I>>(object: I): StateAuctionBidRejected {
    const message = createBaseStateAuctionBidRejected();
    message.auctionId = object.auctionId ?? "";
    message.simulatedBidLamports = object.simulatedBidLamports ?? 0n;
    message.msg = object.msg ?? undefined;
    return message;
  },
};

function createBaseSimulationFailure(): SimulationFailure {
  return { txSignature: "", msg: undefined };
}

export const SimulationFailure: MessageFns<SimulationFailure> = {
  encode(message: SimulationFailure, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txSignature !== "") {
      writer.uint32(10).string(message.txSignature);
    }
    if (message.msg !== undefined) {
      writer.uint32(18).string(message.msg);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SimulationFailure {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimulationFailure();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txSignature = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.msg = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SimulationFailure, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SimulationFailure | SimulationFailure[]> | Iterable<SimulationFailure | SimulationFailure[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SimulationFailure.encode(p).finish()];
        }
      } else {
        yield* [SimulationFailure.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SimulationFailure>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SimulationFailure> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SimulationFailure.decode(p)];
        }
      } else {
        yield* [SimulationFailure.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SimulationFailure {
    return {
      txSignature: isSet(object.txSignature) ? globalThis.String(object.txSignature) : "",
      msg: isSet(object.msg) ? globalThis.String(object.msg) : undefined,
    };
  },

  toJSON(message: SimulationFailure): unknown {
    const obj: any = {};
    if (message.txSignature !== "") {
      obj.txSignature = message.txSignature;
    }
    if (message.msg !== undefined) {
      obj.msg = message.msg;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SimulationFailure>, I>>(base?: I): SimulationFailure {
    return SimulationFailure.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SimulationFailure>, I>>(object: I): SimulationFailure {
    const message = createBaseSimulationFailure();
    message.txSignature = object.txSignature ?? "";
    message.msg = object.msg ?? undefined;
    return message;
  },
};

function createBaseInternalError(): InternalError {
  return { msg: "" };
}

export const InternalError: MessageFns<InternalError> = {
  encode(message: InternalError, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.msg !== "") {
      writer.uint32(10).string(message.msg);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): InternalError {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInternalError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.msg = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<InternalError, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<InternalError | InternalError[]> | Iterable<InternalError | InternalError[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InternalError.encode(p).finish()];
        }
      } else {
        yield* [InternalError.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, InternalError>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<InternalError> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [InternalError.decode(p)];
        }
      } else {
        yield* [InternalError.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): InternalError {
    return { msg: isSet(object.msg) ? globalThis.String(object.msg) : "" };
  },

  toJSON(message: InternalError): unknown {
    const obj: any = {};
    if (message.msg !== "") {
      obj.msg = message.msg;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InternalError>, I>>(base?: I): InternalError {
    return InternalError.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InternalError>, I>>(object: I): InternalError {
    const message = createBaseInternalError();
    message.msg = object.msg ?? "";
    return message;
  },
};

function createBaseDroppedBundle(): DroppedBundle {
  return { msg: "" };
}

export const DroppedBundle: MessageFns<DroppedBundle> = {
  encode(message: DroppedBundle, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.msg !== "") {
      writer.uint32(10).string(message.msg);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DroppedBundle {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDroppedBundle();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.msg = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<DroppedBundle, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<DroppedBundle | DroppedBundle[]> | Iterable<DroppedBundle | DroppedBundle[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [DroppedBundle.encode(p).finish()];
        }
      } else {
        yield* [DroppedBundle.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, DroppedBundle>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<DroppedBundle> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [DroppedBundle.decode(p)];
        }
      } else {
        yield* [DroppedBundle.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): DroppedBundle {
    return { msg: isSet(object.msg) ? globalThis.String(object.msg) : "" };
  },

  toJSON(message: DroppedBundle): unknown {
    const obj: any = {};
    if (message.msg !== "") {
      obj.msg = message.msg;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DroppedBundle>, I>>(base?: I): DroppedBundle {
    return DroppedBundle.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DroppedBundle>, I>>(object: I): DroppedBundle {
    const message = createBaseDroppedBundle();
    message.msg = object.msg ?? "";
    return message;
  },
};

function createBaseFinalized(): Finalized {
  return {};
}

export const Finalized: MessageFns<Finalized> = {
  encode(_: Finalized, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Finalized {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFinalized();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Finalized, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Finalized | Finalized[]> | Iterable<Finalized | Finalized[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Finalized.encode(p).finish()];
        }
      } else {
        yield* [Finalized.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Finalized>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Finalized> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Finalized.decode(p)];
        }
      } else {
        yield* [Finalized.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): Finalized {
    return {};
  },

  toJSON(_: Finalized): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<Finalized>, I>>(base?: I): Finalized {
    return Finalized.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Finalized>, I>>(_: I): Finalized {
    const message = createBaseFinalized();
    return message;
  },
};

function createBaseProcessed(): Processed {
  return { validatorIdentity: "", slot: 0n, bundleIndex: 0n };
}

export const Processed: MessageFns<Processed> = {
  encode(message: Processed, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.validatorIdentity !== "") {
      writer.uint32(10).string(message.validatorIdentity);
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.slot);
    }
    if (message.bundleIndex !== 0n) {
      if (BigInt.asUintN(64, message.bundleIndex) !== message.bundleIndex) {
        throw new globalThis.Error("value provided for field message.bundleIndex of type uint64 too large");
      }
      writer.uint32(24).uint64(message.bundleIndex);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Processed {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProcessed();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.validatorIdentity = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.bundleIndex = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Processed, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Processed | Processed[]> | Iterable<Processed | Processed[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Processed.encode(p).finish()];
        }
      } else {
        yield* [Processed.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Processed>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Processed> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Processed.decode(p)];
        }
      } else {
        yield* [Processed.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Processed {
    return {
      validatorIdentity: isSet(object.validatorIdentity) ? globalThis.String(object.validatorIdentity) : "",
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      bundleIndex: isSet(object.bundleIndex) ? BigInt(object.bundleIndex) : 0n,
    };
  },

  toJSON(message: Processed): unknown {
    const obj: any = {};
    if (message.validatorIdentity !== "") {
      obj.validatorIdentity = message.validatorIdentity;
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.bundleIndex !== 0n) {
      obj.bundleIndex = message.bundleIndex.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Processed>, I>>(base?: I): Processed {
    return Processed.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Processed>, I>>(object: I): Processed {
    const message = createBaseProcessed();
    message.validatorIdentity = object.validatorIdentity ?? "";
    message.slot = object.slot ?? 0n;
    message.bundleIndex = object.bundleIndex ?? 0n;
    return message;
  },
};

function createBaseDropped(): Dropped {
  return { reason: 0 };
}

export const Dropped: MessageFns<Dropped> = {
  encode(message: Dropped, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.reason !== 0) {
      writer.uint32(8).int32(message.reason);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Dropped {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDropped();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.reason = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Dropped, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Dropped | Dropped[]> | Iterable<Dropped | Dropped[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Dropped.encode(p).finish()];
        }
      } else {
        yield* [Dropped.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Dropped>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Dropped> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Dropped.decode(p)];
        }
      } else {
        yield* [Dropped.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Dropped {
    return { reason: isSet(object.reason) ? droppedReasonFromJSON(object.reason) : 0 };
  },

  toJSON(message: Dropped): unknown {
    const obj: any = {};
    if (message.reason !== 0) {
      obj.reason = droppedReasonToJSON(message.reason);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Dropped>, I>>(base?: I): Dropped {
    return Dropped.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Dropped>, I>>(object: I): Dropped {
    const message = createBaseDropped();
    message.reason = object.reason ?? 0;
    return message;
  },
};

function createBaseBundleResult(): BundleResult {
  return {
    bundleId: "",
    accepted: undefined,
    rejected: undefined,
    finalized: undefined,
    processed: undefined,
    dropped: undefined,
  };
}

export const BundleResult: MessageFns<BundleResult> = {
  encode(message: BundleResult, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bundleId !== "") {
      writer.uint32(10).string(message.bundleId);
    }
    if (message.accepted !== undefined) {
      Accepted.encode(message.accepted, writer.uint32(18).fork()).join();
    }
    if (message.rejected !== undefined) {
      Rejected.encode(message.rejected, writer.uint32(26).fork()).join();
    }
    if (message.finalized !== undefined) {
      Finalized.encode(message.finalized, writer.uint32(34).fork()).join();
    }
    if (message.processed !== undefined) {
      Processed.encode(message.processed, writer.uint32(42).fork()).join();
    }
    if (message.dropped !== undefined) {
      Dropped.encode(message.dropped, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BundleResult {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBundleResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bundleId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accepted = Accepted.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.rejected = Rejected.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.finalized = Finalized.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.processed = Processed.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.dropped = Dropped.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<BundleResult, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<BundleResult | BundleResult[]> | Iterable<BundleResult | BundleResult[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BundleResult.encode(p).finish()];
        }
      } else {
        yield* [BundleResult.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, BundleResult>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<BundleResult> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BundleResult.decode(p)];
        }
      } else {
        yield* [BundleResult.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): BundleResult {
    return {
      bundleId: isSet(object.bundleId) ? globalThis.String(object.bundleId) : "",
      accepted: isSet(object.accepted) ? Accepted.fromJSON(object.accepted) : undefined,
      rejected: isSet(object.rejected) ? Rejected.fromJSON(object.rejected) : undefined,
      finalized: isSet(object.finalized) ? Finalized.fromJSON(object.finalized) : undefined,
      processed: isSet(object.processed) ? Processed.fromJSON(object.processed) : undefined,
      dropped: isSet(object.dropped) ? Dropped.fromJSON(object.dropped) : undefined,
    };
  },

  toJSON(message: BundleResult): unknown {
    const obj: any = {};
    if (message.bundleId !== "") {
      obj.bundleId = message.bundleId;
    }
    if (message.accepted !== undefined) {
      obj.accepted = Accepted.toJSON(message.accepted);
    }
    if (message.rejected !== undefined) {
      obj.rejected = Rejected.toJSON(message.rejected);
    }
    if (message.finalized !== undefined) {
      obj.finalized = Finalized.toJSON(message.finalized);
    }
    if (message.processed !== undefined) {
      obj.processed = Processed.toJSON(message.processed);
    }
    if (message.dropped !== undefined) {
      obj.dropped = Dropped.toJSON(message.dropped);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BundleResult>, I>>(base?: I): BundleResult {
    return BundleResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BundleResult>, I>>(object: I): BundleResult {
    const message = createBaseBundleResult();
    message.bundleId = object.bundleId ?? "";
    message.accepted = (object.accepted !== undefined && object.accepted !== null)
      ? Accepted.fromPartial(object.accepted)
      : undefined;
    message.rejected = (object.rejected !== undefined && object.rejected !== null)
      ? Rejected.fromPartial(object.rejected)
      : undefined;
    message.finalized = (object.finalized !== undefined && object.finalized !== null)
      ? Finalized.fromPartial(object.finalized)
      : undefined;
    message.processed = (object.processed !== undefined && object.processed !== null)
      ? Processed.fromPartial(object.processed)
      : undefined;
    message.dropped = (object.dropped !== undefined && object.dropped !== null)
      ? Dropped.fromPartial(object.dropped)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
