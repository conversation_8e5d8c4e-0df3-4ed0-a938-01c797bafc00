// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: auth.proto

/* eslint-disable */
import { Binary<PERSON>eader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientUnaryCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Timestamp } from "./google/protobuf/timestamp";

export enum Role {
  RELAYER = 0,
  SEARCHER = 1,
  VALIDATOR = 2,
  SHREDSTREAM_SUBSCRIBER = 3,
  UNRECOGNIZED = -1,
}

export function roleFromJSON(object: any): Role {
  switch (object) {
    case 0:
    case "RELAYER":
      return Role.RELAYER;
    case 1:
    case "SEARCHER":
      return Role.SEARCHER;
    case 2:
    case "VALIDATOR":
      return Role.VALIDATOR;
    case 3:
    case "SHREDSTREAM_SUBSCRIBER":
      return Role.SHREDSTREAM_SUBSCRIBER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Role.UNRECOGNIZED;
  }
}

export function roleToJSON(object: Role): string {
  switch (object) {
    case Role.RELAYER:
      return "RELAYER";
    case Role.SEARCHER:
      return "SEARCHER";
    case Role.VALIDATOR:
      return "VALIDATOR";
    case Role.SHREDSTREAM_SUBSCRIBER:
      return "SHREDSTREAM_SUBSCRIBER";
    case Role.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface GenerateAuthChallengeRequest {
  /** / Role the client is attempting to generate tokens for. */
  role: Role;
  /** / Client's 32 byte pubkey. */
  pubkey: Buffer;
}

export interface GenerateAuthChallengeResponse {
  challenge: string;
}

export interface GenerateAuthTokensRequest {
  /** / The pre-signed challenge. */
  challenge: string;
  /** / The signing keypair's corresponding 32 byte pubkey. */
  clientPubkey: Buffer;
  /**
   * / The 64 byte signature of the challenge signed by the client's private key. The private key must correspond to
   * the pubkey passed in the [GenerateAuthChallenge] method. The client is expected to sign the challenge token
   * prepended with their pubkey. For example sign(pubkey, challenge).
   */
  signedChallenge: Buffer;
}

export interface Token {
  /** / The token. */
  value: string;
  /** / When the token will expire. */
  expiresAtUtc: Date | undefined;
}

export interface GenerateAuthTokensResponse {
  /** / The token granting access to resources. */
  accessToken:
    | Token
    | undefined;
  /** / The token used to refresh the access_token. This has a longer TTL than the access_token. */
  refreshToken: Token | undefined;
}

export interface RefreshAccessTokenRequest {
  /** / Non-expired refresh token obtained from the [GenerateAuthTokens] method. */
  refreshToken: string;
}

export interface RefreshAccessTokenResponse {
  /** / Fresh access_token. */
  accessToken: Token | undefined;
}

function createBaseGenerateAuthChallengeRequest(): GenerateAuthChallengeRequest {
  return { role: 0, pubkey: Buffer.alloc(0) };
}

export const GenerateAuthChallengeRequest: MessageFns<GenerateAuthChallengeRequest> = {
  encode(message: GenerateAuthChallengeRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.role !== 0) {
      writer.uint32(8).int32(message.role);
    }
    if (message.pubkey.length !== 0) {
      writer.uint32(18).bytes(message.pubkey);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateAuthChallengeRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateAuthChallengeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.role = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.pubkey = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GenerateAuthChallengeRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GenerateAuthChallengeRequest | GenerateAuthChallengeRequest[]>
      | Iterable<GenerateAuthChallengeRequest | GenerateAuthChallengeRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthChallengeRequest.encode(p).finish()];
        }
      } else {
        yield* [GenerateAuthChallengeRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GenerateAuthChallengeRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GenerateAuthChallengeRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthChallengeRequest.decode(p)];
        }
      } else {
        yield* [GenerateAuthChallengeRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GenerateAuthChallengeRequest {
    return {
      role: isSet(object.role) ? roleFromJSON(object.role) : 0,
      pubkey: isSet(object.pubkey) ? Buffer.from(bytesFromBase64(object.pubkey)) : Buffer.alloc(0),
    };
  },

  toJSON(message: GenerateAuthChallengeRequest): unknown {
    const obj: any = {};
    if (message.role !== 0) {
      obj.role = roleToJSON(message.role);
    }
    if (message.pubkey.length !== 0) {
      obj.pubkey = base64FromBytes(message.pubkey);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateAuthChallengeRequest>, I>>(base?: I): GenerateAuthChallengeRequest {
    return GenerateAuthChallengeRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateAuthChallengeRequest>, I>>(object: I): GenerateAuthChallengeRequest {
    const message = createBaseGenerateAuthChallengeRequest();
    message.role = object.role ?? 0;
    message.pubkey = object.pubkey ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseGenerateAuthChallengeResponse(): GenerateAuthChallengeResponse {
  return { challenge: "" };
}

export const GenerateAuthChallengeResponse: MessageFns<GenerateAuthChallengeResponse> = {
  encode(message: GenerateAuthChallengeResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.challenge !== "") {
      writer.uint32(10).string(message.challenge);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateAuthChallengeResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateAuthChallengeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.challenge = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GenerateAuthChallengeResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GenerateAuthChallengeResponse | GenerateAuthChallengeResponse[]>
      | Iterable<GenerateAuthChallengeResponse | GenerateAuthChallengeResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthChallengeResponse.encode(p).finish()];
        }
      } else {
        yield* [GenerateAuthChallengeResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GenerateAuthChallengeResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GenerateAuthChallengeResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthChallengeResponse.decode(p)];
        }
      } else {
        yield* [GenerateAuthChallengeResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GenerateAuthChallengeResponse {
    return { challenge: isSet(object.challenge) ? globalThis.String(object.challenge) : "" };
  },

  toJSON(message: GenerateAuthChallengeResponse): unknown {
    const obj: any = {};
    if (message.challenge !== "") {
      obj.challenge = message.challenge;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateAuthChallengeResponse>, I>>(base?: I): GenerateAuthChallengeResponse {
    return GenerateAuthChallengeResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateAuthChallengeResponse>, I>>(
    object: I,
  ): GenerateAuthChallengeResponse {
    const message = createBaseGenerateAuthChallengeResponse();
    message.challenge = object.challenge ?? "";
    return message;
  },
};

function createBaseGenerateAuthTokensRequest(): GenerateAuthTokensRequest {
  return { challenge: "", clientPubkey: Buffer.alloc(0), signedChallenge: Buffer.alloc(0) };
}

export const GenerateAuthTokensRequest: MessageFns<GenerateAuthTokensRequest> = {
  encode(message: GenerateAuthTokensRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.challenge !== "") {
      writer.uint32(10).string(message.challenge);
    }
    if (message.clientPubkey.length !== 0) {
      writer.uint32(18).bytes(message.clientPubkey);
    }
    if (message.signedChallenge.length !== 0) {
      writer.uint32(26).bytes(message.signedChallenge);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateAuthTokensRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateAuthTokensRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.challenge = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.clientPubkey = Buffer.from(reader.bytes());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.signedChallenge = Buffer.from(reader.bytes());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GenerateAuthTokensRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GenerateAuthTokensRequest | GenerateAuthTokensRequest[]>
      | Iterable<GenerateAuthTokensRequest | GenerateAuthTokensRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthTokensRequest.encode(p).finish()];
        }
      } else {
        yield* [GenerateAuthTokensRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GenerateAuthTokensRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GenerateAuthTokensRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthTokensRequest.decode(p)];
        }
      } else {
        yield* [GenerateAuthTokensRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GenerateAuthTokensRequest {
    return {
      challenge: isSet(object.challenge) ? globalThis.String(object.challenge) : "",
      clientPubkey: isSet(object.clientPubkey) ? Buffer.from(bytesFromBase64(object.clientPubkey)) : Buffer.alloc(0),
      signedChallenge: isSet(object.signedChallenge)
        ? Buffer.from(bytesFromBase64(object.signedChallenge))
        : Buffer.alloc(0),
    };
  },

  toJSON(message: GenerateAuthTokensRequest): unknown {
    const obj: any = {};
    if (message.challenge !== "") {
      obj.challenge = message.challenge;
    }
    if (message.clientPubkey.length !== 0) {
      obj.clientPubkey = base64FromBytes(message.clientPubkey);
    }
    if (message.signedChallenge.length !== 0) {
      obj.signedChallenge = base64FromBytes(message.signedChallenge);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateAuthTokensRequest>, I>>(base?: I): GenerateAuthTokensRequest {
    return GenerateAuthTokensRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateAuthTokensRequest>, I>>(object: I): GenerateAuthTokensRequest {
    const message = createBaseGenerateAuthTokensRequest();
    message.challenge = object.challenge ?? "";
    message.clientPubkey = object.clientPubkey ?? Buffer.alloc(0);
    message.signedChallenge = object.signedChallenge ?? Buffer.alloc(0);
    return message;
  },
};

function createBaseToken(): Token {
  return { value: "", expiresAtUtc: undefined };
}

export const Token: MessageFns<Token> = {
  encode(message: Token, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== "") {
      writer.uint32(10).string(message.value);
    }
    if (message.expiresAtUtc !== undefined) {
      Timestamp.encode(toTimestamp(message.expiresAtUtc), writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Token {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseToken();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.value = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.expiresAtUtc = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Token, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Token | Token[]> | Iterable<Token | Token[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Token.encode(p).finish()];
        }
      } else {
        yield* [Token.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Token>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Token> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Token.decode(p)];
        }
      } else {
        yield* [Token.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Token {
    return {
      value: isSet(object.value) ? globalThis.String(object.value) : "",
      expiresAtUtc: isSet(object.expiresAtUtc) ? fromJsonTimestamp(object.expiresAtUtc) : undefined,
    };
  },

  toJSON(message: Token): unknown {
    const obj: any = {};
    if (message.value !== "") {
      obj.value = message.value;
    }
    if (message.expiresAtUtc !== undefined) {
      obj.expiresAtUtc = message.expiresAtUtc.toISOString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Token>, I>>(base?: I): Token {
    return Token.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Token>, I>>(object: I): Token {
    const message = createBaseToken();
    message.value = object.value ?? "";
    message.expiresAtUtc = object.expiresAtUtc ?? undefined;
    return message;
  },
};

function createBaseGenerateAuthTokensResponse(): GenerateAuthTokensResponse {
  return { accessToken: undefined, refreshToken: undefined };
}

export const GenerateAuthTokensResponse: MessageFns<GenerateAuthTokensResponse> = {
  encode(message: GenerateAuthTokensResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accessToken !== undefined) {
      Token.encode(message.accessToken, writer.uint32(10).fork()).join();
    }
    if (message.refreshToken !== undefined) {
      Token.encode(message.refreshToken, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GenerateAuthTokensResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerateAuthTokensResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accessToken = Token.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.refreshToken = Token.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GenerateAuthTokensResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GenerateAuthTokensResponse | GenerateAuthTokensResponse[]>
      | Iterable<GenerateAuthTokensResponse | GenerateAuthTokensResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthTokensResponse.encode(p).finish()];
        }
      } else {
        yield* [GenerateAuthTokensResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GenerateAuthTokensResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GenerateAuthTokensResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GenerateAuthTokensResponse.decode(p)];
        }
      } else {
        yield* [GenerateAuthTokensResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GenerateAuthTokensResponse {
    return {
      accessToken: isSet(object.accessToken) ? Token.fromJSON(object.accessToken) : undefined,
      refreshToken: isSet(object.refreshToken) ? Token.fromJSON(object.refreshToken) : undefined,
    };
  },

  toJSON(message: GenerateAuthTokensResponse): unknown {
    const obj: any = {};
    if (message.accessToken !== undefined) {
      obj.accessToken = Token.toJSON(message.accessToken);
    }
    if (message.refreshToken !== undefined) {
      obj.refreshToken = Token.toJSON(message.refreshToken);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GenerateAuthTokensResponse>, I>>(base?: I): GenerateAuthTokensResponse {
    return GenerateAuthTokensResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateAuthTokensResponse>, I>>(object: I): GenerateAuthTokensResponse {
    const message = createBaseGenerateAuthTokensResponse();
    message.accessToken = (object.accessToken !== undefined && object.accessToken !== null)
      ? Token.fromPartial(object.accessToken)
      : undefined;
    message.refreshToken = (object.refreshToken !== undefined && object.refreshToken !== null)
      ? Token.fromPartial(object.refreshToken)
      : undefined;
    return message;
  },
};

function createBaseRefreshAccessTokenRequest(): RefreshAccessTokenRequest {
  return { refreshToken: "" };
}

export const RefreshAccessTokenRequest: MessageFns<RefreshAccessTokenRequest> = {
  encode(message: RefreshAccessTokenRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.refreshToken !== "") {
      writer.uint32(10).string(message.refreshToken);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RefreshAccessTokenRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefreshAccessTokenRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.refreshToken = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<RefreshAccessTokenRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<RefreshAccessTokenRequest | RefreshAccessTokenRequest[]>
      | Iterable<RefreshAccessTokenRequest | RefreshAccessTokenRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [RefreshAccessTokenRequest.encode(p).finish()];
        }
      } else {
        yield* [RefreshAccessTokenRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, RefreshAccessTokenRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<RefreshAccessTokenRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [RefreshAccessTokenRequest.decode(p)];
        }
      } else {
        yield* [RefreshAccessTokenRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): RefreshAccessTokenRequest {
    return { refreshToken: isSet(object.refreshToken) ? globalThis.String(object.refreshToken) : "" };
  },

  toJSON(message: RefreshAccessTokenRequest): unknown {
    const obj: any = {};
    if (message.refreshToken !== "") {
      obj.refreshToken = message.refreshToken;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RefreshAccessTokenRequest>, I>>(base?: I): RefreshAccessTokenRequest {
    return RefreshAccessTokenRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshAccessTokenRequest>, I>>(object: I): RefreshAccessTokenRequest {
    const message = createBaseRefreshAccessTokenRequest();
    message.refreshToken = object.refreshToken ?? "";
    return message;
  },
};

function createBaseRefreshAccessTokenResponse(): RefreshAccessTokenResponse {
  return { accessToken: undefined };
}

export const RefreshAccessTokenResponse: MessageFns<RefreshAccessTokenResponse> = {
  encode(message: RefreshAccessTokenResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.accessToken !== undefined) {
      Token.encode(message.accessToken, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RefreshAccessTokenResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefreshAccessTokenResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accessToken = Token.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<RefreshAccessTokenResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<RefreshAccessTokenResponse | RefreshAccessTokenResponse[]>
      | Iterable<RefreshAccessTokenResponse | RefreshAccessTokenResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [RefreshAccessTokenResponse.encode(p).finish()];
        }
      } else {
        yield* [RefreshAccessTokenResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, RefreshAccessTokenResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<RefreshAccessTokenResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [RefreshAccessTokenResponse.decode(p)];
        }
      } else {
        yield* [RefreshAccessTokenResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): RefreshAccessTokenResponse {
    return { accessToken: isSet(object.accessToken) ? Token.fromJSON(object.accessToken) : undefined };
  },

  toJSON(message: RefreshAccessTokenResponse): unknown {
    const obj: any = {};
    if (message.accessToken !== undefined) {
      obj.accessToken = Token.toJSON(message.accessToken);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RefreshAccessTokenResponse>, I>>(base?: I): RefreshAccessTokenResponse {
    return RefreshAccessTokenResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshAccessTokenResponse>, I>>(object: I): RefreshAccessTokenResponse {
    const message = createBaseRefreshAccessTokenResponse();
    message.accessToken = (object.accessToken !== undefined && object.accessToken !== null)
      ? Token.fromPartial(object.accessToken)
      : undefined;
    return message;
  },
};

/** / This service is responsible for issuing auth tokens to clients for API access. */
export type AuthServiceService = typeof AuthServiceService;
export const AuthServiceService = {
  /** / Returns a challenge, client is expected to sign this challenge with an appropriate keypair in order to obtain access tokens. */
  generateAuthChallenge: {
    path: "/auth.AuthService/GenerateAuthChallenge",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GenerateAuthChallengeRequest) =>
      Buffer.from(GenerateAuthChallengeRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GenerateAuthChallengeRequest.decode(value),
    responseSerialize: (value: GenerateAuthChallengeResponse) =>
      Buffer.from(GenerateAuthChallengeResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GenerateAuthChallengeResponse.decode(value),
  },
  /** / Provides the client with the initial pair of auth tokens for API access. */
  generateAuthTokens: {
    path: "/auth.AuthService/GenerateAuthTokens",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GenerateAuthTokensRequest) =>
      Buffer.from(GenerateAuthTokensRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GenerateAuthTokensRequest.decode(value),
    responseSerialize: (value: GenerateAuthTokensResponse) =>
      Buffer.from(GenerateAuthTokensResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GenerateAuthTokensResponse.decode(value),
  },
  /** / Call this method with a non-expired refresh token to obtain a new access token. */
  refreshAccessToken: {
    path: "/auth.AuthService/RefreshAccessToken",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RefreshAccessTokenRequest) =>
      Buffer.from(RefreshAccessTokenRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => RefreshAccessTokenRequest.decode(value),
    responseSerialize: (value: RefreshAccessTokenResponse) =>
      Buffer.from(RefreshAccessTokenResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => RefreshAccessTokenResponse.decode(value),
  },
} as const;

export interface AuthServiceServer extends UntypedServiceImplementation {
  /** / Returns a challenge, client is expected to sign this challenge with an appropriate keypair in order to obtain access tokens. */
  generateAuthChallenge: handleUnaryCall<GenerateAuthChallengeRequest, GenerateAuthChallengeResponse>;
  /** / Provides the client with the initial pair of auth tokens for API access. */
  generateAuthTokens: handleUnaryCall<GenerateAuthTokensRequest, GenerateAuthTokensResponse>;
  /** / Call this method with a non-expired refresh token to obtain a new access token. */
  refreshAccessToken: handleUnaryCall<RefreshAccessTokenRequest, RefreshAccessTokenResponse>;
}

export interface AuthServiceClient extends Client {
  /** / Returns a challenge, client is expected to sign this challenge with an appropriate keypair in order to obtain access tokens. */
  generateAuthChallenge(
    request: GenerateAuthChallengeRequest,
    callback: (error: ServiceError | null, response: GenerateAuthChallengeResponse) => void,
  ): ClientUnaryCall;
  generateAuthChallenge(
    request: GenerateAuthChallengeRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GenerateAuthChallengeResponse) => void,
  ): ClientUnaryCall;
  generateAuthChallenge(
    request: GenerateAuthChallengeRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GenerateAuthChallengeResponse) => void,
  ): ClientUnaryCall;
  /** / Provides the client with the initial pair of auth tokens for API access. */
  generateAuthTokens(
    request: GenerateAuthTokensRequest,
    callback: (error: ServiceError | null, response: GenerateAuthTokensResponse) => void,
  ): ClientUnaryCall;
  generateAuthTokens(
    request: GenerateAuthTokensRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GenerateAuthTokensResponse) => void,
  ): ClientUnaryCall;
  generateAuthTokens(
    request: GenerateAuthTokensRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GenerateAuthTokensResponse) => void,
  ): ClientUnaryCall;
  /** / Call this method with a non-expired refresh token to obtain a new access token. */
  refreshAccessToken(
    request: RefreshAccessTokenRequest,
    callback: (error: ServiceError | null, response: RefreshAccessTokenResponse) => void,
  ): ClientUnaryCall;
  refreshAccessToken(
    request: RefreshAccessTokenRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: RefreshAccessTokenResponse) => void,
  ): ClientUnaryCall;
  refreshAccessToken(
    request: RefreshAccessTokenRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: RefreshAccessTokenResponse) => void,
  ): ClientUnaryCall;
}

export const AuthServiceClient = makeGenericClientConstructor(AuthServiceService, "auth.AuthService") as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): AuthServiceClient;
  service: typeof AuthServiceService;
  serviceName: string;
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
