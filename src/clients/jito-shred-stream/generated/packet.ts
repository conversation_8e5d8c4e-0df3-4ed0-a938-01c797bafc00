// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: packet.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export interface PacketBatch {
  packets: Packet[];
}

export interface Packet {
  data: Buffer;
  meta: Meta | undefined;
}

export interface Meta {
  size: bigint;
  addr: string;
  port: number;
  flags: PacketFlags | undefined;
  senderStake: bigint;
}

export interface PacketFlags {
  discard: boolean;
  forwarded: boolean;
  repair: boolean;
  simpleVoteTx: boolean;
  tracerPacket: boolean;
  fromStakedNode: boolean;
}

function createBasePacketBatch(): PacketBatch {
  return { packets: [] };
}

export const PacketBatch: MessageFns<PacketBatch> = {
  encode(message: PacketBatch, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.packets) {
      Packet.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PacketBatch {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePacketBatch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.packets.push(Packet.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PacketBatch, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PacketBatch | PacketBatch[]> | Iterable<PacketBatch | PacketBatch[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PacketBatch.encode(p).finish()];
        }
      } else {
        yield* [PacketBatch.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PacketBatch>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PacketBatch> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PacketBatch.decode(p)];
        }
      } else {
        yield* [PacketBatch.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PacketBatch {
    return {
      packets: globalThis.Array.isArray(object?.packets) ? object.packets.map((e: any) => Packet.fromJSON(e)) : [],
    };
  },

  toJSON(message: PacketBatch): unknown {
    const obj: any = {};
    if (message.packets?.length) {
      obj.packets = message.packets.map((e) => Packet.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PacketBatch>, I>>(base?: I): PacketBatch {
    return PacketBatch.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PacketBatch>, I>>(object: I): PacketBatch {
    const message = createBasePacketBatch();
    message.packets = object.packets?.map((e) => Packet.fromPartial(e)) || [];
    return message;
  },
};

function createBasePacket(): Packet {
  return { data: Buffer.alloc(0), meta: undefined };
}

export const Packet: MessageFns<Packet> = {
  encode(message: Packet, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.data.length !== 0) {
      writer.uint32(10).bytes(message.data);
    }
    if (message.meta !== undefined) {
      Meta.encode(message.meta, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Packet {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePacket();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.data = Buffer.from(reader.bytes());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.meta = Meta.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Packet, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<Packet | Packet[]> | Iterable<Packet | Packet[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Packet.encode(p).finish()];
        }
      } else {
        yield* [Packet.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Packet>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Packet> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Packet.decode(p)];
        }
      } else {
        yield* [Packet.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Packet {
    return {
      data: isSet(object.data) ? Buffer.from(bytesFromBase64(object.data)) : Buffer.alloc(0),
      meta: isSet(object.meta) ? Meta.fromJSON(object.meta) : undefined,
    };
  },

  toJSON(message: Packet): unknown {
    const obj: any = {};
    if (message.data.length !== 0) {
      obj.data = base64FromBytes(message.data);
    }
    if (message.meta !== undefined) {
      obj.meta = Meta.toJSON(message.meta);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Packet>, I>>(base?: I): Packet {
    return Packet.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Packet>, I>>(object: I): Packet {
    const message = createBasePacket();
    message.data = object.data ?? Buffer.alloc(0);
    message.meta = (object.meta !== undefined && object.meta !== null) ? Meta.fromPartial(object.meta) : undefined;
    return message;
  },
};

function createBaseMeta(): Meta {
  return { size: 0n, addr: "", port: 0, flags: undefined, senderStake: 0n };
}

export const Meta: MessageFns<Meta> = {
  encode(message: Meta, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.size !== 0n) {
      if (BigInt.asUintN(64, message.size) !== message.size) {
        throw new globalThis.Error("value provided for field message.size of type uint64 too large");
      }
      writer.uint32(8).uint64(message.size);
    }
    if (message.addr !== "") {
      writer.uint32(18).string(message.addr);
    }
    if (message.port !== 0) {
      writer.uint32(24).uint32(message.port);
    }
    if (message.flags !== undefined) {
      PacketFlags.encode(message.flags, writer.uint32(34).fork()).join();
    }
    if (message.senderStake !== 0n) {
      if (BigInt.asUintN(64, message.senderStake) !== message.senderStake) {
        throw new globalThis.Error("value provided for field message.senderStake of type uint64 too large");
      }
      writer.uint32(40).uint64(message.senderStake);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Meta {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.size = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.addr = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.port = reader.uint32();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.flags = PacketFlags.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.senderStake = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<Meta, Uint8Array>
  async *encodeTransform(source: AsyncIterable<Meta | Meta[]> | Iterable<Meta | Meta[]>): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Meta.encode(p).finish()];
        }
      } else {
        yield* [Meta.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, Meta>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<Meta> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [Meta.decode(p)];
        }
      } else {
        yield* [Meta.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): Meta {
    return {
      size: isSet(object.size) ? BigInt(object.size) : 0n,
      addr: isSet(object.addr) ? globalThis.String(object.addr) : "",
      port: isSet(object.port) ? globalThis.Number(object.port) : 0,
      flags: isSet(object.flags) ? PacketFlags.fromJSON(object.flags) : undefined,
      senderStake: isSet(object.senderStake) ? BigInt(object.senderStake) : 0n,
    };
  },

  toJSON(message: Meta): unknown {
    const obj: any = {};
    if (message.size !== 0n) {
      obj.size = message.size.toString();
    }
    if (message.addr !== "") {
      obj.addr = message.addr;
    }
    if (message.port !== 0) {
      obj.port = Math.round(message.port);
    }
    if (message.flags !== undefined) {
      obj.flags = PacketFlags.toJSON(message.flags);
    }
    if (message.senderStake !== 0n) {
      obj.senderStake = message.senderStake.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Meta>, I>>(base?: I): Meta {
    return Meta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Meta>, I>>(object: I): Meta {
    const message = createBaseMeta();
    message.size = object.size ?? 0n;
    message.addr = object.addr ?? "";
    message.port = object.port ?? 0;
    message.flags = (object.flags !== undefined && object.flags !== null)
      ? PacketFlags.fromPartial(object.flags)
      : undefined;
    message.senderStake = object.senderStake ?? 0n;
    return message;
  },
};

function createBasePacketFlags(): PacketFlags {
  return {
    discard: false,
    forwarded: false,
    repair: false,
    simpleVoteTx: false,
    tracerPacket: false,
    fromStakedNode: false,
  };
}

export const PacketFlags: MessageFns<PacketFlags> = {
  encode(message: PacketFlags, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.discard !== false) {
      writer.uint32(8).bool(message.discard);
    }
    if (message.forwarded !== false) {
      writer.uint32(16).bool(message.forwarded);
    }
    if (message.repair !== false) {
      writer.uint32(24).bool(message.repair);
    }
    if (message.simpleVoteTx !== false) {
      writer.uint32(32).bool(message.simpleVoteTx);
    }
    if (message.tracerPacket !== false) {
      writer.uint32(40).bool(message.tracerPacket);
    }
    if (message.fromStakedNode !== false) {
      writer.uint32(48).bool(message.fromStakedNode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PacketFlags {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePacketFlags();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.discard = reader.bool();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.forwarded = reader.bool();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.repair = reader.bool();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.simpleVoteTx = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.tracerPacket = reader.bool();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.fromStakedNode = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PacketFlags, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PacketFlags | PacketFlags[]> | Iterable<PacketFlags | PacketFlags[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PacketFlags.encode(p).finish()];
        }
      } else {
        yield* [PacketFlags.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PacketFlags>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PacketFlags> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PacketFlags.decode(p)];
        }
      } else {
        yield* [PacketFlags.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PacketFlags {
    return {
      discard: isSet(object.discard) ? globalThis.Boolean(object.discard) : false,
      forwarded: isSet(object.forwarded) ? globalThis.Boolean(object.forwarded) : false,
      repair: isSet(object.repair) ? globalThis.Boolean(object.repair) : false,
      simpleVoteTx: isSet(object.simpleVoteTx) ? globalThis.Boolean(object.simpleVoteTx) : false,
      tracerPacket: isSet(object.tracerPacket) ? globalThis.Boolean(object.tracerPacket) : false,
      fromStakedNode: isSet(object.fromStakedNode) ? globalThis.Boolean(object.fromStakedNode) : false,
    };
  },

  toJSON(message: PacketFlags): unknown {
    const obj: any = {};
    if (message.discard !== false) {
      obj.discard = message.discard;
    }
    if (message.forwarded !== false) {
      obj.forwarded = message.forwarded;
    }
    if (message.repair !== false) {
      obj.repair = message.repair;
    }
    if (message.simpleVoteTx !== false) {
      obj.simpleVoteTx = message.simpleVoteTx;
    }
    if (message.tracerPacket !== false) {
      obj.tracerPacket = message.tracerPacket;
    }
    if (message.fromStakedNode !== false) {
      obj.fromStakedNode = message.fromStakedNode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PacketFlags>, I>>(base?: I): PacketFlags {
    return PacketFlags.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PacketFlags>, I>>(object: I): PacketFlags {
    const message = createBasePacketFlags();
    message.discard = object.discard ?? false;
    message.forwarded = object.forwarded ?? false;
    message.repair = object.repair ?? false;
    message.simpleVoteTx = object.simpleVoteTx ?? false;
    message.tracerPacket = object.tracerPacket ?? false;
    message.fromStakedNode = object.fromStakedNode ?? false;
    return message;
  },
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
