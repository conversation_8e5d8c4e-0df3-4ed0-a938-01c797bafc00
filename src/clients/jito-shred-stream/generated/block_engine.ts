// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: block_engine.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientDuplexStream,
  type ClientOptions,
  type ClientReadableStream,
  type ClientUnaryCall,
  type handleBidiStreamingCall,
  type handleServerStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { BundleUuid } from "./bundle";
import { PacketBatch } from "./packet";
import { Header, Heartbeat } from "./shared";

export interface SubscribePacketsRequest {
}

export interface SubscribePacketsResponse {
  header: Header | undefined;
  batch: PacketBatch | undefined;
}

export interface SubscribeBundlesRequest {
}

export interface SubscribeBundlesResponse {
  bundles: BundleUuid[];
}

export interface BlockBuilderFeeInfoRequest {
}

export interface BlockBuilderFeeInfoResponse {
  pubkey: string;
  /** commission (0-100) */
  commission: bigint;
}

export interface AccountsOfInterest {
  /** use * for all accounts */
  accounts: string[];
}

export interface AccountsOfInterestRequest {
}

export interface AccountsOfInterestUpdate {
  accounts: string[];
}

export interface ProgramsOfInterestRequest {
}

export interface ProgramsOfInterestUpdate {
  programs: string[];
}

/**
 * A series of packets with an expiration attached to them.
 * The header contains a timestamp for when this packet was generated.
 * The expiry is how long the packet batches have before they expire and are forwarded to the validator.
 * This provides a more censorship resistant method to MEV than block engines receiving packets directly.
 */
export interface ExpiringPacketBatch {
  header: Header | undefined;
  batch: PacketBatch | undefined;
  expiryMs: number;
}

/**
 * Packets and heartbeats are sent over the same stream.
 * ExpiringPacketBatches have an expiration attached to them so the block engine can track
 * how long it has until the relayer forwards the packets to the validator.
 * Heartbeats contain a timestamp from the system and is used as a simple and naive time-sync mechanism
 * so the block engine has some idea on how far their clocks are apart.
 */
export interface PacketBatchUpdate {
  batches?: ExpiringPacketBatch | undefined;
  heartbeat?: Heartbeat | undefined;
}

export interface StartExpiringPacketStreamResponse {
  heartbeat: Heartbeat | undefined;
}

function createBaseSubscribePacketsRequest(): SubscribePacketsRequest {
  return {};
}

export const SubscribePacketsRequest: MessageFns<SubscribePacketsRequest> = {
  encode(_: SubscribePacketsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribePacketsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribePacketsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribePacketsRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribePacketsRequest | SubscribePacketsRequest[]>
      | Iterable<SubscribePacketsRequest | SubscribePacketsRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribePacketsRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribePacketsRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribePacketsRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsRequest.decode(p)];
        }
      } else {
        yield* [SubscribePacketsRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribePacketsRequest {
    return {};
  },

  toJSON(_: SubscribePacketsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribePacketsRequest>, I>>(base?: I): SubscribePacketsRequest {
    return SubscribePacketsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribePacketsRequest>, I>>(_: I): SubscribePacketsRequest {
    const message = createBaseSubscribePacketsRequest();
    return message;
  },
};

function createBaseSubscribePacketsResponse(): SubscribePacketsResponse {
  return { header: undefined, batch: undefined };
}

export const SubscribePacketsResponse: MessageFns<SubscribePacketsResponse> = {
  encode(message: SubscribePacketsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      Header.encode(message.header, writer.uint32(10).fork()).join();
    }
    if (message.batch !== undefined) {
      PacketBatch.encode(message.batch, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribePacketsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribePacketsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = Header.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.batch = PacketBatch.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribePacketsResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribePacketsResponse | SubscribePacketsResponse[]>
      | Iterable<SubscribePacketsResponse | SubscribePacketsResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsResponse.encode(p).finish()];
        }
      } else {
        yield* [SubscribePacketsResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribePacketsResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribePacketsResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsResponse.decode(p)];
        }
      } else {
        yield* [SubscribePacketsResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribePacketsResponse {
    return {
      header: isSet(object.header) ? Header.fromJSON(object.header) : undefined,
      batch: isSet(object.batch) ? PacketBatch.fromJSON(object.batch) : undefined,
    };
  },

  toJSON(message: SubscribePacketsResponse): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = Header.toJSON(message.header);
    }
    if (message.batch !== undefined) {
      obj.batch = PacketBatch.toJSON(message.batch);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribePacketsResponse>, I>>(base?: I): SubscribePacketsResponse {
    return SubscribePacketsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribePacketsResponse>, I>>(object: I): SubscribePacketsResponse {
    const message = createBaseSubscribePacketsResponse();
    message.header = (object.header !== undefined && object.header !== null)
      ? Header.fromPartial(object.header)
      : undefined;
    message.batch = (object.batch !== undefined && object.batch !== null)
      ? PacketBatch.fromPartial(object.batch)
      : undefined;
    return message;
  },
};

function createBaseSubscribeBundlesRequest(): SubscribeBundlesRequest {
  return {};
}

export const SubscribeBundlesRequest: MessageFns<SubscribeBundlesRequest> = {
  encode(_: SubscribeBundlesRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeBundlesRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeBundlesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeBundlesRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeBundlesRequest | SubscribeBundlesRequest[]>
      | Iterable<SubscribeBundlesRequest | SubscribeBundlesRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeBundlesRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeBundlesRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeBundlesRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeBundlesRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeBundlesRequest.decode(p)];
        }
      } else {
        yield* [SubscribeBundlesRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeBundlesRequest {
    return {};
  },

  toJSON(_: SubscribeBundlesRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeBundlesRequest>, I>>(base?: I): SubscribeBundlesRequest {
    return SubscribeBundlesRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeBundlesRequest>, I>>(_: I): SubscribeBundlesRequest {
    const message = createBaseSubscribeBundlesRequest();
    return message;
  },
};

function createBaseSubscribeBundlesResponse(): SubscribeBundlesResponse {
  return { bundles: [] };
}

export const SubscribeBundlesResponse: MessageFns<SubscribeBundlesResponse> = {
  encode(message: SubscribeBundlesResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.bundles) {
      BundleUuid.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeBundlesResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeBundlesResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bundles.push(BundleUuid.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeBundlesResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeBundlesResponse | SubscribeBundlesResponse[]>
      | Iterable<SubscribeBundlesResponse | SubscribeBundlesResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeBundlesResponse.encode(p).finish()];
        }
      } else {
        yield* [SubscribeBundlesResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeBundlesResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeBundlesResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeBundlesResponse.decode(p)];
        }
      } else {
        yield* [SubscribeBundlesResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribeBundlesResponse {
    return {
      bundles: globalThis.Array.isArray(object?.bundles) ? object.bundles.map((e: any) => BundleUuid.fromJSON(e)) : [],
    };
  },

  toJSON(message: SubscribeBundlesResponse): unknown {
    const obj: any = {};
    if (message.bundles?.length) {
      obj.bundles = message.bundles.map((e) => BundleUuid.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeBundlesResponse>, I>>(base?: I): SubscribeBundlesResponse {
    return SubscribeBundlesResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeBundlesResponse>, I>>(object: I): SubscribeBundlesResponse {
    const message = createBaseSubscribeBundlesResponse();
    message.bundles = object.bundles?.map((e) => BundleUuid.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBlockBuilderFeeInfoRequest(): BlockBuilderFeeInfoRequest {
  return {};
}

export const BlockBuilderFeeInfoRequest: MessageFns<BlockBuilderFeeInfoRequest> = {
  encode(_: BlockBuilderFeeInfoRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BlockBuilderFeeInfoRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBlockBuilderFeeInfoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<BlockBuilderFeeInfoRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<BlockBuilderFeeInfoRequest | BlockBuilderFeeInfoRequest[]>
      | Iterable<BlockBuilderFeeInfoRequest | BlockBuilderFeeInfoRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BlockBuilderFeeInfoRequest.encode(p).finish()];
        }
      } else {
        yield* [BlockBuilderFeeInfoRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, BlockBuilderFeeInfoRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<BlockBuilderFeeInfoRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BlockBuilderFeeInfoRequest.decode(p)];
        }
      } else {
        yield* [BlockBuilderFeeInfoRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): BlockBuilderFeeInfoRequest {
    return {};
  },

  toJSON(_: BlockBuilderFeeInfoRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<BlockBuilderFeeInfoRequest>, I>>(base?: I): BlockBuilderFeeInfoRequest {
    return BlockBuilderFeeInfoRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BlockBuilderFeeInfoRequest>, I>>(_: I): BlockBuilderFeeInfoRequest {
    const message = createBaseBlockBuilderFeeInfoRequest();
    return message;
  },
};

function createBaseBlockBuilderFeeInfoResponse(): BlockBuilderFeeInfoResponse {
  return { pubkey: "", commission: 0n };
}

export const BlockBuilderFeeInfoResponse: MessageFns<BlockBuilderFeeInfoResponse> = {
  encode(message: BlockBuilderFeeInfoResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pubkey !== "") {
      writer.uint32(10).string(message.pubkey);
    }
    if (message.commission !== 0n) {
      if (BigInt.asUintN(64, message.commission) !== message.commission) {
        throw new globalThis.Error("value provided for field message.commission of type uint64 too large");
      }
      writer.uint32(16).uint64(message.commission);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): BlockBuilderFeeInfoResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBlockBuilderFeeInfoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.pubkey = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.commission = reader.uint64() as bigint;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<BlockBuilderFeeInfoResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<BlockBuilderFeeInfoResponse | BlockBuilderFeeInfoResponse[]>
      | Iterable<BlockBuilderFeeInfoResponse | BlockBuilderFeeInfoResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BlockBuilderFeeInfoResponse.encode(p).finish()];
        }
      } else {
        yield* [BlockBuilderFeeInfoResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, BlockBuilderFeeInfoResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<BlockBuilderFeeInfoResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [BlockBuilderFeeInfoResponse.decode(p)];
        }
      } else {
        yield* [BlockBuilderFeeInfoResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): BlockBuilderFeeInfoResponse {
    return {
      pubkey: isSet(object.pubkey) ? globalThis.String(object.pubkey) : "",
      commission: isSet(object.commission) ? BigInt(object.commission) : 0n,
    };
  },

  toJSON(message: BlockBuilderFeeInfoResponse): unknown {
    const obj: any = {};
    if (message.pubkey !== "") {
      obj.pubkey = message.pubkey;
    }
    if (message.commission !== 0n) {
      obj.commission = message.commission.toString();
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BlockBuilderFeeInfoResponse>, I>>(base?: I): BlockBuilderFeeInfoResponse {
    return BlockBuilderFeeInfoResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BlockBuilderFeeInfoResponse>, I>>(object: I): BlockBuilderFeeInfoResponse {
    const message = createBaseBlockBuilderFeeInfoResponse();
    message.pubkey = object.pubkey ?? "";
    message.commission = object.commission ?? 0n;
    return message;
  },
};

function createBaseAccountsOfInterest(): AccountsOfInterest {
  return { accounts: [] };
}

export const AccountsOfInterest: MessageFns<AccountsOfInterest> = {
  encode(message: AccountsOfInterest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accounts) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AccountsOfInterest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAccountsOfInterest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accounts.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<AccountsOfInterest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<AccountsOfInterest | AccountsOfInterest[]>
      | Iterable<AccountsOfInterest | AccountsOfInterest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [AccountsOfInterest.encode(p).finish()];
        }
      } else {
        yield* [AccountsOfInterest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, AccountsOfInterest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<AccountsOfInterest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [AccountsOfInterest.decode(p)];
        }
      } else {
        yield* [AccountsOfInterest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): AccountsOfInterest {
    return {
      accounts: globalThis.Array.isArray(object?.accounts) ? object.accounts.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: AccountsOfInterest): unknown {
    const obj: any = {};
    if (message.accounts?.length) {
      obj.accounts = message.accounts;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AccountsOfInterest>, I>>(base?: I): AccountsOfInterest {
    return AccountsOfInterest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AccountsOfInterest>, I>>(object: I): AccountsOfInterest {
    const message = createBaseAccountsOfInterest();
    message.accounts = object.accounts?.map((e) => e) || [];
    return message;
  },
};

function createBaseAccountsOfInterestRequest(): AccountsOfInterestRequest {
  return {};
}

export const AccountsOfInterestRequest: MessageFns<AccountsOfInterestRequest> = {
  encode(_: AccountsOfInterestRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AccountsOfInterestRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAccountsOfInterestRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<AccountsOfInterestRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<AccountsOfInterestRequest | AccountsOfInterestRequest[]>
      | Iterable<AccountsOfInterestRequest | AccountsOfInterestRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [AccountsOfInterestRequest.encode(p).finish()];
        }
      } else {
        yield* [AccountsOfInterestRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, AccountsOfInterestRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<AccountsOfInterestRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [AccountsOfInterestRequest.decode(p)];
        }
      } else {
        yield* [AccountsOfInterestRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): AccountsOfInterestRequest {
    return {};
  },

  toJSON(_: AccountsOfInterestRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AccountsOfInterestRequest>, I>>(base?: I): AccountsOfInterestRequest {
    return AccountsOfInterestRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AccountsOfInterestRequest>, I>>(_: I): AccountsOfInterestRequest {
    const message = createBaseAccountsOfInterestRequest();
    return message;
  },
};

function createBaseAccountsOfInterestUpdate(): AccountsOfInterestUpdate {
  return { accounts: [] };
}

export const AccountsOfInterestUpdate: MessageFns<AccountsOfInterestUpdate> = {
  encode(message: AccountsOfInterestUpdate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accounts) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AccountsOfInterestUpdate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAccountsOfInterestUpdate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accounts.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<AccountsOfInterestUpdate, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<AccountsOfInterestUpdate | AccountsOfInterestUpdate[]>
      | Iterable<AccountsOfInterestUpdate | AccountsOfInterestUpdate[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [AccountsOfInterestUpdate.encode(p).finish()];
        }
      } else {
        yield* [AccountsOfInterestUpdate.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, AccountsOfInterestUpdate>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<AccountsOfInterestUpdate> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [AccountsOfInterestUpdate.decode(p)];
        }
      } else {
        yield* [AccountsOfInterestUpdate.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): AccountsOfInterestUpdate {
    return {
      accounts: globalThis.Array.isArray(object?.accounts) ? object.accounts.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: AccountsOfInterestUpdate): unknown {
    const obj: any = {};
    if (message.accounts?.length) {
      obj.accounts = message.accounts;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AccountsOfInterestUpdate>, I>>(base?: I): AccountsOfInterestUpdate {
    return AccountsOfInterestUpdate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AccountsOfInterestUpdate>, I>>(object: I): AccountsOfInterestUpdate {
    const message = createBaseAccountsOfInterestUpdate();
    message.accounts = object.accounts?.map((e) => e) || [];
    return message;
  },
};

function createBaseProgramsOfInterestRequest(): ProgramsOfInterestRequest {
  return {};
}

export const ProgramsOfInterestRequest: MessageFns<ProgramsOfInterestRequest> = {
  encode(_: ProgramsOfInterestRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProgramsOfInterestRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProgramsOfInterestRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ProgramsOfInterestRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ProgramsOfInterestRequest | ProgramsOfInterestRequest[]>
      | Iterable<ProgramsOfInterestRequest | ProgramsOfInterestRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ProgramsOfInterestRequest.encode(p).finish()];
        }
      } else {
        yield* [ProgramsOfInterestRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ProgramsOfInterestRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ProgramsOfInterestRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ProgramsOfInterestRequest.decode(p)];
        }
      } else {
        yield* [ProgramsOfInterestRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): ProgramsOfInterestRequest {
    return {};
  },

  toJSON(_: ProgramsOfInterestRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<ProgramsOfInterestRequest>, I>>(base?: I): ProgramsOfInterestRequest {
    return ProgramsOfInterestRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProgramsOfInterestRequest>, I>>(_: I): ProgramsOfInterestRequest {
    const message = createBaseProgramsOfInterestRequest();
    return message;
  },
};

function createBaseProgramsOfInterestUpdate(): ProgramsOfInterestUpdate {
  return { programs: [] };
}

export const ProgramsOfInterestUpdate: MessageFns<ProgramsOfInterestUpdate> = {
  encode(message: ProgramsOfInterestUpdate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.programs) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ProgramsOfInterestUpdate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProgramsOfInterestUpdate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.programs.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ProgramsOfInterestUpdate, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ProgramsOfInterestUpdate | ProgramsOfInterestUpdate[]>
      | Iterable<ProgramsOfInterestUpdate | ProgramsOfInterestUpdate[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ProgramsOfInterestUpdate.encode(p).finish()];
        }
      } else {
        yield* [ProgramsOfInterestUpdate.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ProgramsOfInterestUpdate>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ProgramsOfInterestUpdate> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ProgramsOfInterestUpdate.decode(p)];
        }
      } else {
        yield* [ProgramsOfInterestUpdate.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ProgramsOfInterestUpdate {
    return {
      programs: globalThis.Array.isArray(object?.programs) ? object.programs.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: ProgramsOfInterestUpdate): unknown {
    const obj: any = {};
    if (message.programs?.length) {
      obj.programs = message.programs;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProgramsOfInterestUpdate>, I>>(base?: I): ProgramsOfInterestUpdate {
    return ProgramsOfInterestUpdate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProgramsOfInterestUpdate>, I>>(object: I): ProgramsOfInterestUpdate {
    const message = createBaseProgramsOfInterestUpdate();
    message.programs = object.programs?.map((e) => e) || [];
    return message;
  },
};

function createBaseExpiringPacketBatch(): ExpiringPacketBatch {
  return { header: undefined, batch: undefined, expiryMs: 0 };
}

export const ExpiringPacketBatch: MessageFns<ExpiringPacketBatch> = {
  encode(message: ExpiringPacketBatch, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      Header.encode(message.header, writer.uint32(10).fork()).join();
    }
    if (message.batch !== undefined) {
      PacketBatch.encode(message.batch, writer.uint32(18).fork()).join();
    }
    if (message.expiryMs !== 0) {
      writer.uint32(24).uint32(message.expiryMs);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ExpiringPacketBatch {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExpiringPacketBatch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = Header.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.batch = PacketBatch.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.expiryMs = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ExpiringPacketBatch, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ExpiringPacketBatch | ExpiringPacketBatch[]>
      | Iterable<ExpiringPacketBatch | ExpiringPacketBatch[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ExpiringPacketBatch.encode(p).finish()];
        }
      } else {
        yield* [ExpiringPacketBatch.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ExpiringPacketBatch>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ExpiringPacketBatch> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ExpiringPacketBatch.decode(p)];
        }
      } else {
        yield* [ExpiringPacketBatch.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ExpiringPacketBatch {
    return {
      header: isSet(object.header) ? Header.fromJSON(object.header) : undefined,
      batch: isSet(object.batch) ? PacketBatch.fromJSON(object.batch) : undefined,
      expiryMs: isSet(object.expiryMs) ? globalThis.Number(object.expiryMs) : 0,
    };
  },

  toJSON(message: ExpiringPacketBatch): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = Header.toJSON(message.header);
    }
    if (message.batch !== undefined) {
      obj.batch = PacketBatch.toJSON(message.batch);
    }
    if (message.expiryMs !== 0) {
      obj.expiryMs = Math.round(message.expiryMs);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ExpiringPacketBatch>, I>>(base?: I): ExpiringPacketBatch {
    return ExpiringPacketBatch.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExpiringPacketBatch>, I>>(object: I): ExpiringPacketBatch {
    const message = createBaseExpiringPacketBatch();
    message.header = (object.header !== undefined && object.header !== null)
      ? Header.fromPartial(object.header)
      : undefined;
    message.batch = (object.batch !== undefined && object.batch !== null)
      ? PacketBatch.fromPartial(object.batch)
      : undefined;
    message.expiryMs = object.expiryMs ?? 0;
    return message;
  },
};

function createBasePacketBatchUpdate(): PacketBatchUpdate {
  return { batches: undefined, heartbeat: undefined };
}

export const PacketBatchUpdate: MessageFns<PacketBatchUpdate> = {
  encode(message: PacketBatchUpdate, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.batches !== undefined) {
      ExpiringPacketBatch.encode(message.batches, writer.uint32(10).fork()).join();
    }
    if (message.heartbeat !== undefined) {
      Heartbeat.encode(message.heartbeat, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): PacketBatchUpdate {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePacketBatchUpdate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.batches = ExpiringPacketBatch.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.heartbeat = Heartbeat.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<PacketBatchUpdate, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<PacketBatchUpdate | PacketBatchUpdate[]> | Iterable<PacketBatchUpdate | PacketBatchUpdate[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PacketBatchUpdate.encode(p).finish()];
        }
      } else {
        yield* [PacketBatchUpdate.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, PacketBatchUpdate>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<PacketBatchUpdate> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [PacketBatchUpdate.decode(p)];
        }
      } else {
        yield* [PacketBatchUpdate.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): PacketBatchUpdate {
    return {
      batches: isSet(object.batches) ? ExpiringPacketBatch.fromJSON(object.batches) : undefined,
      heartbeat: isSet(object.heartbeat) ? Heartbeat.fromJSON(object.heartbeat) : undefined,
    };
  },

  toJSON(message: PacketBatchUpdate): unknown {
    const obj: any = {};
    if (message.batches !== undefined) {
      obj.batches = ExpiringPacketBatch.toJSON(message.batches);
    }
    if (message.heartbeat !== undefined) {
      obj.heartbeat = Heartbeat.toJSON(message.heartbeat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PacketBatchUpdate>, I>>(base?: I): PacketBatchUpdate {
    return PacketBatchUpdate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PacketBatchUpdate>, I>>(object: I): PacketBatchUpdate {
    const message = createBasePacketBatchUpdate();
    message.batches = (object.batches !== undefined && object.batches !== null)
      ? ExpiringPacketBatch.fromPartial(object.batches)
      : undefined;
    message.heartbeat = (object.heartbeat !== undefined && object.heartbeat !== null)
      ? Heartbeat.fromPartial(object.heartbeat)
      : undefined;
    return message;
  },
};

function createBaseStartExpiringPacketStreamResponse(): StartExpiringPacketStreamResponse {
  return { heartbeat: undefined };
}

export const StartExpiringPacketStreamResponse: MessageFns<StartExpiringPacketStreamResponse> = {
  encode(message: StartExpiringPacketStreamResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.heartbeat !== undefined) {
      Heartbeat.encode(message.heartbeat, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): StartExpiringPacketStreamResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStartExpiringPacketStreamResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.heartbeat = Heartbeat.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<StartExpiringPacketStreamResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<StartExpiringPacketStreamResponse | StartExpiringPacketStreamResponse[]>
      | Iterable<StartExpiringPacketStreamResponse | StartExpiringPacketStreamResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [StartExpiringPacketStreamResponse.encode(p).finish()];
        }
      } else {
        yield* [StartExpiringPacketStreamResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, StartExpiringPacketStreamResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<StartExpiringPacketStreamResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [StartExpiringPacketStreamResponse.decode(p)];
        }
      } else {
        yield* [StartExpiringPacketStreamResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): StartExpiringPacketStreamResponse {
    return { heartbeat: isSet(object.heartbeat) ? Heartbeat.fromJSON(object.heartbeat) : undefined };
  },

  toJSON(message: StartExpiringPacketStreamResponse): unknown {
    const obj: any = {};
    if (message.heartbeat !== undefined) {
      obj.heartbeat = Heartbeat.toJSON(message.heartbeat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<StartExpiringPacketStreamResponse>, I>>(
    base?: I,
  ): StartExpiringPacketStreamResponse {
    return StartExpiringPacketStreamResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StartExpiringPacketStreamResponse>, I>>(
    object: I,
  ): StartExpiringPacketStreamResponse {
    const message = createBaseStartExpiringPacketStreamResponse();
    message.heartbeat = (object.heartbeat !== undefined && object.heartbeat !== null)
      ? Heartbeat.fromPartial(object.heartbeat)
      : undefined;
    return message;
  },
};

/** / Validators can connect to Block Engines to receive packets and bundles. */
export type BlockEngineValidatorService = typeof BlockEngineValidatorService;
export const BlockEngineValidatorService = {
  /** / Validators can subscribe to the block engine to receive a stream of packets */
  subscribePackets: {
    path: "/block_engine.BlockEngineValidator/SubscribePackets",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribePacketsRequest) => Buffer.from(SubscribePacketsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribePacketsRequest.decode(value),
    responseSerialize: (value: SubscribePacketsResponse) =>
      Buffer.from(SubscribePacketsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribePacketsResponse.decode(value),
  },
  /** / Validators can subscribe to the block engine to receive a stream of simulated and profitable bundles */
  subscribeBundles: {
    path: "/block_engine.BlockEngineValidator/SubscribeBundles",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribeBundlesRequest) => Buffer.from(SubscribeBundlesRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeBundlesRequest.decode(value),
    responseSerialize: (value: SubscribeBundlesResponse) =>
      Buffer.from(SubscribeBundlesResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribeBundlesResponse.decode(value),
  },
  /**
   * Block builders can optionally collect fees. This returns fee information if a block builder wants to
   * collect one.
   */
  getBlockBuilderFeeInfo: {
    path: "/block_engine.BlockEngineValidator/GetBlockBuilderFeeInfo",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: BlockBuilderFeeInfoRequest) =>
      Buffer.from(BlockBuilderFeeInfoRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => BlockBuilderFeeInfoRequest.decode(value),
    responseSerialize: (value: BlockBuilderFeeInfoResponse) =>
      Buffer.from(BlockBuilderFeeInfoResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => BlockBuilderFeeInfoResponse.decode(value),
  },
} as const;

export interface BlockEngineValidatorServer extends UntypedServiceImplementation {
  /** / Validators can subscribe to the block engine to receive a stream of packets */
  subscribePackets: handleServerStreamingCall<SubscribePacketsRequest, SubscribePacketsResponse>;
  /** / Validators can subscribe to the block engine to receive a stream of simulated and profitable bundles */
  subscribeBundles: handleServerStreamingCall<SubscribeBundlesRequest, SubscribeBundlesResponse>;
  /**
   * Block builders can optionally collect fees. This returns fee information if a block builder wants to
   * collect one.
   */
  getBlockBuilderFeeInfo: handleUnaryCall<BlockBuilderFeeInfoRequest, BlockBuilderFeeInfoResponse>;
}

export interface BlockEngineValidatorClient extends Client {
  /** / Validators can subscribe to the block engine to receive a stream of packets */
  subscribePackets(
    request: SubscribePacketsRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<SubscribePacketsResponse>;
  subscribePackets(
    request: SubscribePacketsRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<SubscribePacketsResponse>;
  /** / Validators can subscribe to the block engine to receive a stream of simulated and profitable bundles */
  subscribeBundles(
    request: SubscribeBundlesRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<SubscribeBundlesResponse>;
  subscribeBundles(
    request: SubscribeBundlesRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<SubscribeBundlesResponse>;
  /**
   * Block builders can optionally collect fees. This returns fee information if a block builder wants to
   * collect one.
   */
  getBlockBuilderFeeInfo(
    request: BlockBuilderFeeInfoRequest,
    callback: (error: ServiceError | null, response: BlockBuilderFeeInfoResponse) => void,
  ): ClientUnaryCall;
  getBlockBuilderFeeInfo(
    request: BlockBuilderFeeInfoRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: BlockBuilderFeeInfoResponse) => void,
  ): ClientUnaryCall;
  getBlockBuilderFeeInfo(
    request: BlockBuilderFeeInfoRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: BlockBuilderFeeInfoResponse) => void,
  ): ClientUnaryCall;
}

export const BlockEngineValidatorClient = makeGenericClientConstructor(
  BlockEngineValidatorService,
  "block_engine.BlockEngineValidator",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): BlockEngineValidatorClient;
  service: typeof BlockEngineValidatorService;
  serviceName: string;
};

/**
 * / Relayers can forward packets to Block Engines.
 * / Block Engines provide an AccountsOfInterest field to only send transactions that are of interest.
 */
export type BlockEngineRelayerService = typeof BlockEngineRelayerService;
export const BlockEngineRelayerService = {
  /**
   * / The block engine feeds accounts of interest (AOI) updates to the relayer periodically.
   * / For all transactions the relayer receives, it forwards transactions to the block engine which write-lock
   * / any of the accounts in the AOI.
   */
  subscribeAccountsOfInterest: {
    path: "/block_engine.BlockEngineRelayer/SubscribeAccountsOfInterest",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: AccountsOfInterestRequest) =>
      Buffer.from(AccountsOfInterestRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => AccountsOfInterestRequest.decode(value),
    responseSerialize: (value: AccountsOfInterestUpdate) =>
      Buffer.from(AccountsOfInterestUpdate.encode(value).finish()),
    responseDeserialize: (value: Buffer) => AccountsOfInterestUpdate.decode(value),
  },
  subscribeProgramsOfInterest: {
    path: "/block_engine.BlockEngineRelayer/SubscribeProgramsOfInterest",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: ProgramsOfInterestRequest) =>
      Buffer.from(ProgramsOfInterestRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => ProgramsOfInterestRequest.decode(value),
    responseSerialize: (value: ProgramsOfInterestUpdate) =>
      Buffer.from(ProgramsOfInterestUpdate.encode(value).finish()),
    responseDeserialize: (value: Buffer) => ProgramsOfInterestUpdate.decode(value),
  },
  /**
   * Validators can subscribe to packets from the relayer and receive a multiplexed signal that contains a mixture
   * of packets and heartbeats.
   * NOTE: This is a bi-directional stream due to a bug with how Envoy handles half closed client-side streams.
   * The issue is being tracked here: https://github.com/envoyproxy/envoy/issues/22748. In the meantime, the
   * server will stream heartbeats to clients at some reasonable cadence.
   */
  startExpiringPacketStream: {
    path: "/block_engine.BlockEngineRelayer/StartExpiringPacketStream",
    requestStream: true,
    responseStream: true,
    requestSerialize: (value: PacketBatchUpdate) => Buffer.from(PacketBatchUpdate.encode(value).finish()),
    requestDeserialize: (value: Buffer) => PacketBatchUpdate.decode(value),
    responseSerialize: (value: StartExpiringPacketStreamResponse) =>
      Buffer.from(StartExpiringPacketStreamResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => StartExpiringPacketStreamResponse.decode(value),
  },
} as const;

export interface BlockEngineRelayerServer extends UntypedServiceImplementation {
  /**
   * / The block engine feeds accounts of interest (AOI) updates to the relayer periodically.
   * / For all transactions the relayer receives, it forwards transactions to the block engine which write-lock
   * / any of the accounts in the AOI.
   */
  subscribeAccountsOfInterest: handleServerStreamingCall<AccountsOfInterestRequest, AccountsOfInterestUpdate>;
  subscribeProgramsOfInterest: handleServerStreamingCall<ProgramsOfInterestRequest, ProgramsOfInterestUpdate>;
  /**
   * Validators can subscribe to packets from the relayer and receive a multiplexed signal that contains a mixture
   * of packets and heartbeats.
   * NOTE: This is a bi-directional stream due to a bug with how Envoy handles half closed client-side streams.
   * The issue is being tracked here: https://github.com/envoyproxy/envoy/issues/22748. In the meantime, the
   * server will stream heartbeats to clients at some reasonable cadence.
   */
  startExpiringPacketStream: handleBidiStreamingCall<PacketBatchUpdate, StartExpiringPacketStreamResponse>;
}

export interface BlockEngineRelayerClient extends Client {
  /**
   * / The block engine feeds accounts of interest (AOI) updates to the relayer periodically.
   * / For all transactions the relayer receives, it forwards transactions to the block engine which write-lock
   * / any of the accounts in the AOI.
   */
  subscribeAccountsOfInterest(
    request: AccountsOfInterestRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<AccountsOfInterestUpdate>;
  subscribeAccountsOfInterest(
    request: AccountsOfInterestRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<AccountsOfInterestUpdate>;
  subscribeProgramsOfInterest(
    request: ProgramsOfInterestRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<ProgramsOfInterestUpdate>;
  subscribeProgramsOfInterest(
    request: ProgramsOfInterestRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<ProgramsOfInterestUpdate>;
  /**
   * Validators can subscribe to packets from the relayer and receive a multiplexed signal that contains a mixture
   * of packets and heartbeats.
   * NOTE: This is a bi-directional stream due to a bug with how Envoy handles half closed client-side streams.
   * The issue is being tracked here: https://github.com/envoyproxy/envoy/issues/22748. In the meantime, the
   * server will stream heartbeats to clients at some reasonable cadence.
   */
  startExpiringPacketStream(): ClientDuplexStream<PacketBatchUpdate, StartExpiringPacketStreamResponse>;
  startExpiringPacketStream(
    options: Partial<CallOptions>,
  ): ClientDuplexStream<PacketBatchUpdate, StartExpiringPacketStreamResponse>;
  startExpiringPacketStream(
    metadata: Metadata,
    options?: Partial<CallOptions>,
  ): ClientDuplexStream<PacketBatchUpdate, StartExpiringPacketStreamResponse>;
}

export const BlockEngineRelayerClient = makeGenericClientConstructor(
  BlockEngineRelayerService,
  "block_engine.BlockEngineRelayer",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): BlockEngineRelayerClient;
  service: typeof BlockEngineRelayerService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
