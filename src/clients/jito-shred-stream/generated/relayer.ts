// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: relayer.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientReadableStream,
  type ClientUnaryCall,
  type handleServerStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { PacketBatch } from "./packet";
import { Header, Heartbeat, Socket } from "./shared";

export interface GetTpuConfigsRequest {
}

export interface GetTpuConfigsResponse {
  tpu: Socket | undefined;
  tpuForward: Socket | undefined;
}

export interface SubscribePacketsRequest {
}

export interface SubscribePacketsResponse {
  header: Header | undefined;
  heartbeat?: Heartbeat | undefined;
  batch?: PacketBatch | undefined;
}

function createBaseGetTpuConfigsRequest(): GetTpuConfigsRequest {
  return {};
}

export const GetTpuConfigsRequest: MessageFns<GetTpuConfigsRequest> = {
  encode(_: GetTpuConfigsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTpuConfigsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTpuConfigsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetTpuConfigsRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetTpuConfigsRequest | GetTpuConfigsRequest[]>
      | Iterable<GetTpuConfigsRequest | GetTpuConfigsRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTpuConfigsRequest.encode(p).finish()];
        }
      } else {
        yield* [GetTpuConfigsRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetTpuConfigsRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetTpuConfigsRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTpuConfigsRequest.decode(p)];
        }
      } else {
        yield* [GetTpuConfigsRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): GetTpuConfigsRequest {
    return {};
  },

  toJSON(_: GetTpuConfigsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetTpuConfigsRequest>, I>>(base?: I): GetTpuConfigsRequest {
    return GetTpuConfigsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTpuConfigsRequest>, I>>(_: I): GetTpuConfigsRequest {
    const message = createBaseGetTpuConfigsRequest();
    return message;
  },
};

function createBaseGetTpuConfigsResponse(): GetTpuConfigsResponse {
  return { tpu: undefined, tpuForward: undefined };
}

export const GetTpuConfigsResponse: MessageFns<GetTpuConfigsResponse> = {
  encode(message: GetTpuConfigsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.tpu !== undefined) {
      Socket.encode(message.tpu, writer.uint32(10).fork()).join();
    }
    if (message.tpuForward !== undefined) {
      Socket.encode(message.tpuForward, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTpuConfigsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTpuConfigsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.tpu = Socket.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.tpuForward = Socket.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetTpuConfigsResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetTpuConfigsResponse | GetTpuConfigsResponse[]>
      | Iterable<GetTpuConfigsResponse | GetTpuConfigsResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTpuConfigsResponse.encode(p).finish()];
        }
      } else {
        yield* [GetTpuConfigsResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetTpuConfigsResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetTpuConfigsResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTpuConfigsResponse.decode(p)];
        }
      } else {
        yield* [GetTpuConfigsResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetTpuConfigsResponse {
    return {
      tpu: isSet(object.tpu) ? Socket.fromJSON(object.tpu) : undefined,
      tpuForward: isSet(object.tpuForward) ? Socket.fromJSON(object.tpuForward) : undefined,
    };
  },

  toJSON(message: GetTpuConfigsResponse): unknown {
    const obj: any = {};
    if (message.tpu !== undefined) {
      obj.tpu = Socket.toJSON(message.tpu);
    }
    if (message.tpuForward !== undefined) {
      obj.tpuForward = Socket.toJSON(message.tpuForward);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetTpuConfigsResponse>, I>>(base?: I): GetTpuConfigsResponse {
    return GetTpuConfigsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTpuConfigsResponse>, I>>(object: I): GetTpuConfigsResponse {
    const message = createBaseGetTpuConfigsResponse();
    message.tpu = (object.tpu !== undefined && object.tpu !== null) ? Socket.fromPartial(object.tpu) : undefined;
    message.tpuForward = (object.tpuForward !== undefined && object.tpuForward !== null)
      ? Socket.fromPartial(object.tpuForward)
      : undefined;
    return message;
  },
};

function createBaseSubscribePacketsRequest(): SubscribePacketsRequest {
  return {};
}

export const SubscribePacketsRequest: MessageFns<SubscribePacketsRequest> = {
  encode(_: SubscribePacketsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribePacketsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribePacketsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribePacketsRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribePacketsRequest | SubscribePacketsRequest[]>
      | Iterable<SubscribePacketsRequest | SubscribePacketsRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribePacketsRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribePacketsRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribePacketsRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsRequest.decode(p)];
        }
      } else {
        yield* [SubscribePacketsRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribePacketsRequest {
    return {};
  },

  toJSON(_: SubscribePacketsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribePacketsRequest>, I>>(base?: I): SubscribePacketsRequest {
    return SubscribePacketsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribePacketsRequest>, I>>(_: I): SubscribePacketsRequest {
    const message = createBaseSubscribePacketsRequest();
    return message;
  },
};

function createBaseSubscribePacketsResponse(): SubscribePacketsResponse {
  return { header: undefined, heartbeat: undefined, batch: undefined };
}

export const SubscribePacketsResponse: MessageFns<SubscribePacketsResponse> = {
  encode(message: SubscribePacketsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      Header.encode(message.header, writer.uint32(10).fork()).join();
    }
    if (message.heartbeat !== undefined) {
      Heartbeat.encode(message.heartbeat, writer.uint32(18).fork()).join();
    }
    if (message.batch !== undefined) {
      PacketBatch.encode(message.batch, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribePacketsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribePacketsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = Header.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.heartbeat = Heartbeat.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.batch = PacketBatch.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribePacketsResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribePacketsResponse | SubscribePacketsResponse[]>
      | Iterable<SubscribePacketsResponse | SubscribePacketsResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsResponse.encode(p).finish()];
        }
      } else {
        yield* [SubscribePacketsResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribePacketsResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribePacketsResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribePacketsResponse.decode(p)];
        }
      } else {
        yield* [SubscribePacketsResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SubscribePacketsResponse {
    return {
      header: isSet(object.header) ? Header.fromJSON(object.header) : undefined,
      heartbeat: isSet(object.heartbeat) ? Heartbeat.fromJSON(object.heartbeat) : undefined,
      batch: isSet(object.batch) ? PacketBatch.fromJSON(object.batch) : undefined,
    };
  },

  toJSON(message: SubscribePacketsResponse): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = Header.toJSON(message.header);
    }
    if (message.heartbeat !== undefined) {
      obj.heartbeat = Heartbeat.toJSON(message.heartbeat);
    }
    if (message.batch !== undefined) {
      obj.batch = PacketBatch.toJSON(message.batch);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribePacketsResponse>, I>>(base?: I): SubscribePacketsResponse {
    return SubscribePacketsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribePacketsResponse>, I>>(object: I): SubscribePacketsResponse {
    const message = createBaseSubscribePacketsResponse();
    message.header = (object.header !== undefined && object.header !== null)
      ? Header.fromPartial(object.header)
      : undefined;
    message.heartbeat = (object.heartbeat !== undefined && object.heartbeat !== null)
      ? Heartbeat.fromPartial(object.heartbeat)
      : undefined;
    message.batch = (object.batch !== undefined && object.batch !== null)
      ? PacketBatch.fromPartial(object.batch)
      : undefined;
    return message;
  },
};

/**
 * / Relayers offer a TPU and TPU forward proxy for Solana validators.
 * / Validators can connect and fetch the TPU configuration for the relayer and start to advertise the
 * / relayer's information in gossip.
 * / They can also subscribe to packets which arrived on the TPU ports at the relayer
 */
export type RelayerService = typeof RelayerService;
export const RelayerService = {
  /**
   * The relayer has TPU and TPU forward sockets that validators can leverage.
   * A validator can fetch this config and change its TPU and TPU forward port in gossip.
   */
  getTpuConfigs: {
    path: "/relayer.Relayer/GetTpuConfigs",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetTpuConfigsRequest) => Buffer.from(GetTpuConfigsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetTpuConfigsRequest.decode(value),
    responseSerialize: (value: GetTpuConfigsResponse) => Buffer.from(GetTpuConfigsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetTpuConfigsResponse.decode(value),
  },
  /**
   * Validators can subscribe to packets from the relayer and receive a multiplexed signal that contains a mixture
   * of packets and heartbeats
   */
  subscribePackets: {
    path: "/relayer.Relayer/SubscribePackets",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribePacketsRequest) => Buffer.from(SubscribePacketsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribePacketsRequest.decode(value),
    responseSerialize: (value: SubscribePacketsResponse) =>
      Buffer.from(SubscribePacketsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SubscribePacketsResponse.decode(value),
  },
} as const;

export interface RelayerServer extends UntypedServiceImplementation {
  /**
   * The relayer has TPU and TPU forward sockets that validators can leverage.
   * A validator can fetch this config and change its TPU and TPU forward port in gossip.
   */
  getTpuConfigs: handleUnaryCall<GetTpuConfigsRequest, GetTpuConfigsResponse>;
  /**
   * Validators can subscribe to packets from the relayer and receive a multiplexed signal that contains a mixture
   * of packets and heartbeats
   */
  subscribePackets: handleServerStreamingCall<SubscribePacketsRequest, SubscribePacketsResponse>;
}

export interface RelayerClient extends Client {
  /**
   * The relayer has TPU and TPU forward sockets that validators can leverage.
   * A validator can fetch this config and change its TPU and TPU forward port in gossip.
   */
  getTpuConfigs(
    request: GetTpuConfigsRequest,
    callback: (error: ServiceError | null, response: GetTpuConfigsResponse) => void,
  ): ClientUnaryCall;
  getTpuConfigs(
    request: GetTpuConfigsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetTpuConfigsResponse) => void,
  ): ClientUnaryCall;
  getTpuConfigs(
    request: GetTpuConfigsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetTpuConfigsResponse) => void,
  ): ClientUnaryCall;
  /**
   * Validators can subscribe to packets from the relayer and receive a multiplexed signal that contains a mixture
   * of packets and heartbeats
   */
  subscribePackets(
    request: SubscribePacketsRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<SubscribePacketsResponse>;
  subscribePackets(
    request: SubscribePacketsRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<SubscribePacketsResponse>;
}

export const RelayerClient = makeGenericClientConstructor(RelayerService, "relayer.Relayer") as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): RelayerClient;
  service: typeof RelayerService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
