// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: searcher.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientReadableStream,
  type ClientUnaryCall,
  type handleServerStreamingCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { Bundle, BundleResult } from "./bundle";

export interface SlotList {
  slots: bigint[];
}

export interface ConnectedLeadersResponse {
  /** Mapping of validator pubkey to leader slots for the current epoch. */
  connectedValidators: { [key: string]: SlotList };
}

export interface ConnectedLeadersResponse_ConnectedValidatorsEntry {
  key: string;
  value: SlotList | undefined;
}

export interface SendBundleRequest {
  bundle: Bundle | undefined;
}

export interface SendBundleResponse {
  /** server uuid for the bundle */
  uuid: string;
}

export interface NextScheduledLeaderRequest {
  /** Defaults to the currently connected region if no region provided. */
  regions: string[];
}

export interface NextScheduledLeaderResponse {
  /** the current slot the backend is on */
  currentSlot: bigint;
  /** the slot of the next leader */
  nextLeaderSlot: bigint;
  /** the identity pubkey (base58) of the next leader */
  nextLeaderIdentity: string;
  /** the block engine region of the next leader */
  nextLeaderRegion: string;
}

export interface ConnectedLeadersRequest {
}

export interface ConnectedLeadersRegionedRequest {
  /** Defaults to the currently connected region if no region provided. */
  regions: string[];
}

export interface ConnectedLeadersRegionedResponse {
  connectedValidators: { [key: string]: ConnectedLeadersResponse };
}

export interface ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry {
  key: string;
  value: ConnectedLeadersResponse | undefined;
}

export interface GetTipAccountsRequest {
}

export interface GetTipAccountsResponse {
  accounts: string[];
}

export interface SubscribeBundleResultsRequest {
}

export interface GetRegionsRequest {
}

export interface GetRegionsResponse {
  /** The region the client is currently connected to */
  currentRegion: string;
  /**
   * Regions that are online and ready for connections
   * All regions: https://jito-labs.gitbook.io/mev/systems/connecting/mainnet
   */
  availableRegions: string[];
}

function createBaseSlotList(): SlotList {
  return { slots: [] };
}

export const SlotList: MessageFns<SlotList> = {
  encode(message: SlotList, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    writer.uint32(10).fork();
    for (const v of message.slots) {
      if (BigInt.asUintN(64, v) !== v) {
        throw new globalThis.Error("a value provided in array field slots of type uint64 is too large");
      }
      writer.uint64(v);
    }
    writer.join();
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SlotList {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSlotList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag === 8) {
            message.slots.push(reader.uint64() as bigint);

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.slots.push(reader.uint64() as bigint);
            }

            continue;
          }

          break;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SlotList, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SlotList | SlotList[]> | Iterable<SlotList | SlotList[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SlotList.encode(p).finish()];
        }
      } else {
        yield* [SlotList.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SlotList>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SlotList> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SlotList.decode(p)];
        }
      } else {
        yield* [SlotList.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SlotList {
    return { slots: globalThis.Array.isArray(object?.slots) ? object.slots.map((e: any) => BigInt(e)) : [] };
  },

  toJSON(message: SlotList): unknown {
    const obj: any = {};
    if (message.slots?.length) {
      obj.slots = message.slots.map((e) => e.toString());
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SlotList>, I>>(base?: I): SlotList {
    return SlotList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SlotList>, I>>(object: I): SlotList {
    const message = createBaseSlotList();
    message.slots = object.slots?.map((e) => e) || [];
    return message;
  },
};

function createBaseConnectedLeadersResponse(): ConnectedLeadersResponse {
  return { connectedValidators: {} };
}

export const ConnectedLeadersResponse: MessageFns<ConnectedLeadersResponse> = {
  encode(message: ConnectedLeadersResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.connectedValidators).forEach(([key, value]) => {
      ConnectedLeadersResponse_ConnectedValidatorsEntry.encode({ key: key as any, value }, writer.uint32(10).fork())
        .join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConnectedLeadersResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConnectedLeadersResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = ConnectedLeadersResponse_ConnectedValidatorsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.connectedValidators[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConnectedLeadersResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ConnectedLeadersResponse | ConnectedLeadersResponse[]>
      | Iterable<ConnectedLeadersResponse | ConnectedLeadersResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersResponse.encode(p).finish()];
        }
      } else {
        yield* [ConnectedLeadersResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConnectedLeadersResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConnectedLeadersResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersResponse.decode(p)];
        }
      } else {
        yield* [ConnectedLeadersResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ConnectedLeadersResponse {
    return {
      connectedValidators: isObject(object.connectedValidators)
        ? Object.entries(object.connectedValidators).reduce<{ [key: string]: SlotList }>((acc, [key, value]) => {
          acc[key] = SlotList.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: ConnectedLeadersResponse): unknown {
    const obj: any = {};
    if (message.connectedValidators) {
      const entries = Object.entries(message.connectedValidators);
      if (entries.length > 0) {
        obj.connectedValidators = {};
        entries.forEach(([k, v]) => {
          obj.connectedValidators[k] = SlotList.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConnectedLeadersResponse>, I>>(base?: I): ConnectedLeadersResponse {
    return ConnectedLeadersResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConnectedLeadersResponse>, I>>(object: I): ConnectedLeadersResponse {
    const message = createBaseConnectedLeadersResponse();
    message.connectedValidators = Object.entries(object.connectedValidators ?? {}).reduce<{ [key: string]: SlotList }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SlotList.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseConnectedLeadersResponse_ConnectedValidatorsEntry(): ConnectedLeadersResponse_ConnectedValidatorsEntry {
  return { key: "", value: undefined };
}

export const ConnectedLeadersResponse_ConnectedValidatorsEntry: MessageFns<
  ConnectedLeadersResponse_ConnectedValidatorsEntry
> = {
  encode(
    message: ConnectedLeadersResponse_ConnectedValidatorsEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      SlotList.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConnectedLeadersResponse_ConnectedValidatorsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConnectedLeadersResponse_ConnectedValidatorsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = SlotList.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConnectedLeadersResponse_ConnectedValidatorsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<
        ConnectedLeadersResponse_ConnectedValidatorsEntry | ConnectedLeadersResponse_ConnectedValidatorsEntry[]
      >
      | Iterable<
        ConnectedLeadersResponse_ConnectedValidatorsEntry | ConnectedLeadersResponse_ConnectedValidatorsEntry[]
      >,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersResponse_ConnectedValidatorsEntry.encode(p).finish()];
        }
      } else {
        yield* [ConnectedLeadersResponse_ConnectedValidatorsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConnectedLeadersResponse_ConnectedValidatorsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConnectedLeadersResponse_ConnectedValidatorsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersResponse_ConnectedValidatorsEntry.decode(p)];
        }
      } else {
        yield* [ConnectedLeadersResponse_ConnectedValidatorsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ConnectedLeadersResponse_ConnectedValidatorsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? SlotList.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ConnectedLeadersResponse_ConnectedValidatorsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = SlotList.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConnectedLeadersResponse_ConnectedValidatorsEntry>, I>>(
    base?: I,
  ): ConnectedLeadersResponse_ConnectedValidatorsEntry {
    return ConnectedLeadersResponse_ConnectedValidatorsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConnectedLeadersResponse_ConnectedValidatorsEntry>, I>>(
    object: I,
  ): ConnectedLeadersResponse_ConnectedValidatorsEntry {
    const message = createBaseConnectedLeadersResponse_ConnectedValidatorsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? SlotList.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseSendBundleRequest(): SendBundleRequest {
  return { bundle: undefined };
}

export const SendBundleRequest: MessageFns<SendBundleRequest> = {
  encode(message: SendBundleRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.bundle !== undefined) {
      Bundle.encode(message.bundle, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendBundleRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendBundleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.bundle = Bundle.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SendBundleRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<SendBundleRequest | SendBundleRequest[]> | Iterable<SendBundleRequest | SendBundleRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SendBundleRequest.encode(p).finish()];
        }
      } else {
        yield* [SendBundleRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SendBundleRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SendBundleRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SendBundleRequest.decode(p)];
        }
      } else {
        yield* [SendBundleRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SendBundleRequest {
    return { bundle: isSet(object.bundle) ? Bundle.fromJSON(object.bundle) : undefined };
  },

  toJSON(message: SendBundleRequest): unknown {
    const obj: any = {};
    if (message.bundle !== undefined) {
      obj.bundle = Bundle.toJSON(message.bundle);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendBundleRequest>, I>>(base?: I): SendBundleRequest {
    return SendBundleRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendBundleRequest>, I>>(object: I): SendBundleRequest {
    const message = createBaseSendBundleRequest();
    message.bundle = (object.bundle !== undefined && object.bundle !== null)
      ? Bundle.fromPartial(object.bundle)
      : undefined;
    return message;
  },
};

function createBaseSendBundleResponse(): SendBundleResponse {
  return { uuid: "" };
}

export const SendBundleResponse: MessageFns<SendBundleResponse> = {
  encode(message: SendBundleResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.uuid !== "") {
      writer.uint32(10).string(message.uuid);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SendBundleResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSendBundleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.uuid = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SendBundleResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SendBundleResponse | SendBundleResponse[]>
      | Iterable<SendBundleResponse | SendBundleResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SendBundleResponse.encode(p).finish()];
        }
      } else {
        yield* [SendBundleResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SendBundleResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SendBundleResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SendBundleResponse.decode(p)];
        }
      } else {
        yield* [SendBundleResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): SendBundleResponse {
    return { uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "" };
  },

  toJSON(message: SendBundleResponse): unknown {
    const obj: any = {};
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SendBundleResponse>, I>>(base?: I): SendBundleResponse {
    return SendBundleResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendBundleResponse>, I>>(object: I): SendBundleResponse {
    const message = createBaseSendBundleResponse();
    message.uuid = object.uuid ?? "";
    return message;
  },
};

function createBaseNextScheduledLeaderRequest(): NextScheduledLeaderRequest {
  return { regions: [] };
}

export const NextScheduledLeaderRequest: MessageFns<NextScheduledLeaderRequest> = {
  encode(message: NextScheduledLeaderRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.regions) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NextScheduledLeaderRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNextScheduledLeaderRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.regions.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<NextScheduledLeaderRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<NextScheduledLeaderRequest | NextScheduledLeaderRequest[]>
      | Iterable<NextScheduledLeaderRequest | NextScheduledLeaderRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [NextScheduledLeaderRequest.encode(p).finish()];
        }
      } else {
        yield* [NextScheduledLeaderRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, NextScheduledLeaderRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<NextScheduledLeaderRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [NextScheduledLeaderRequest.decode(p)];
        }
      } else {
        yield* [NextScheduledLeaderRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): NextScheduledLeaderRequest {
    return {
      regions: globalThis.Array.isArray(object?.regions) ? object.regions.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: NextScheduledLeaderRequest): unknown {
    const obj: any = {};
    if (message.regions?.length) {
      obj.regions = message.regions;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NextScheduledLeaderRequest>, I>>(base?: I): NextScheduledLeaderRequest {
    return NextScheduledLeaderRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NextScheduledLeaderRequest>, I>>(object: I): NextScheduledLeaderRequest {
    const message = createBaseNextScheduledLeaderRequest();
    message.regions = object.regions?.map((e) => e) || [];
    return message;
  },
};

function createBaseNextScheduledLeaderResponse(): NextScheduledLeaderResponse {
  return { currentSlot: 0n, nextLeaderSlot: 0n, nextLeaderIdentity: "", nextLeaderRegion: "" };
}

export const NextScheduledLeaderResponse: MessageFns<NextScheduledLeaderResponse> = {
  encode(message: NextScheduledLeaderResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentSlot !== 0n) {
      if (BigInt.asUintN(64, message.currentSlot) !== message.currentSlot) {
        throw new globalThis.Error("value provided for field message.currentSlot of type uint64 too large");
      }
      writer.uint32(8).uint64(message.currentSlot);
    }
    if (message.nextLeaderSlot !== 0n) {
      if (BigInt.asUintN(64, message.nextLeaderSlot) !== message.nextLeaderSlot) {
        throw new globalThis.Error("value provided for field message.nextLeaderSlot of type uint64 too large");
      }
      writer.uint32(16).uint64(message.nextLeaderSlot);
    }
    if (message.nextLeaderIdentity !== "") {
      writer.uint32(26).string(message.nextLeaderIdentity);
    }
    if (message.nextLeaderRegion !== "") {
      writer.uint32(34).string(message.nextLeaderRegion);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): NextScheduledLeaderResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNextScheduledLeaderResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.currentSlot = reader.uint64() as bigint;
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.nextLeaderSlot = reader.uint64() as bigint;
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.nextLeaderIdentity = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.nextLeaderRegion = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<NextScheduledLeaderResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<NextScheduledLeaderResponse | NextScheduledLeaderResponse[]>
      | Iterable<NextScheduledLeaderResponse | NextScheduledLeaderResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [NextScheduledLeaderResponse.encode(p).finish()];
        }
      } else {
        yield* [NextScheduledLeaderResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, NextScheduledLeaderResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<NextScheduledLeaderResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [NextScheduledLeaderResponse.decode(p)];
        }
      } else {
        yield* [NextScheduledLeaderResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): NextScheduledLeaderResponse {
    return {
      currentSlot: isSet(object.currentSlot) ? BigInt(object.currentSlot) : 0n,
      nextLeaderSlot: isSet(object.nextLeaderSlot) ? BigInt(object.nextLeaderSlot) : 0n,
      nextLeaderIdentity: isSet(object.nextLeaderIdentity) ? globalThis.String(object.nextLeaderIdentity) : "",
      nextLeaderRegion: isSet(object.nextLeaderRegion) ? globalThis.String(object.nextLeaderRegion) : "",
    };
  },

  toJSON(message: NextScheduledLeaderResponse): unknown {
    const obj: any = {};
    if (message.currentSlot !== 0n) {
      obj.currentSlot = message.currentSlot.toString();
    }
    if (message.nextLeaderSlot !== 0n) {
      obj.nextLeaderSlot = message.nextLeaderSlot.toString();
    }
    if (message.nextLeaderIdentity !== "") {
      obj.nextLeaderIdentity = message.nextLeaderIdentity;
    }
    if (message.nextLeaderRegion !== "") {
      obj.nextLeaderRegion = message.nextLeaderRegion;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<NextScheduledLeaderResponse>, I>>(base?: I): NextScheduledLeaderResponse {
    return NextScheduledLeaderResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NextScheduledLeaderResponse>, I>>(object: I): NextScheduledLeaderResponse {
    const message = createBaseNextScheduledLeaderResponse();
    message.currentSlot = object.currentSlot ?? 0n;
    message.nextLeaderSlot = object.nextLeaderSlot ?? 0n;
    message.nextLeaderIdentity = object.nextLeaderIdentity ?? "";
    message.nextLeaderRegion = object.nextLeaderRegion ?? "";
    return message;
  },
};

function createBaseConnectedLeadersRequest(): ConnectedLeadersRequest {
  return {};
}

export const ConnectedLeadersRequest: MessageFns<ConnectedLeadersRequest> = {
  encode(_: ConnectedLeadersRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConnectedLeadersRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConnectedLeadersRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConnectedLeadersRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ConnectedLeadersRequest | ConnectedLeadersRequest[]>
      | Iterable<ConnectedLeadersRequest | ConnectedLeadersRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRequest.encode(p).finish()];
        }
      } else {
        yield* [ConnectedLeadersRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConnectedLeadersRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConnectedLeadersRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRequest.decode(p)];
        }
      } else {
        yield* [ConnectedLeadersRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): ConnectedLeadersRequest {
    return {};
  },

  toJSON(_: ConnectedLeadersRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<ConnectedLeadersRequest>, I>>(base?: I): ConnectedLeadersRequest {
    return ConnectedLeadersRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConnectedLeadersRequest>, I>>(_: I): ConnectedLeadersRequest {
    const message = createBaseConnectedLeadersRequest();
    return message;
  },
};

function createBaseConnectedLeadersRegionedRequest(): ConnectedLeadersRegionedRequest {
  return { regions: [] };
}

export const ConnectedLeadersRegionedRequest: MessageFns<ConnectedLeadersRegionedRequest> = {
  encode(message: ConnectedLeadersRegionedRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.regions) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConnectedLeadersRegionedRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConnectedLeadersRegionedRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.regions.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConnectedLeadersRegionedRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ConnectedLeadersRegionedRequest | ConnectedLeadersRegionedRequest[]>
      | Iterable<ConnectedLeadersRegionedRequest | ConnectedLeadersRegionedRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRegionedRequest.encode(p).finish()];
        }
      } else {
        yield* [ConnectedLeadersRegionedRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConnectedLeadersRegionedRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConnectedLeadersRegionedRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRegionedRequest.decode(p)];
        }
      } else {
        yield* [ConnectedLeadersRegionedRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ConnectedLeadersRegionedRequest {
    return {
      regions: globalThis.Array.isArray(object?.regions) ? object.regions.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: ConnectedLeadersRegionedRequest): unknown {
    const obj: any = {};
    if (message.regions?.length) {
      obj.regions = message.regions;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConnectedLeadersRegionedRequest>, I>>(base?: I): ConnectedLeadersRegionedRequest {
    return ConnectedLeadersRegionedRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConnectedLeadersRegionedRequest>, I>>(
    object: I,
  ): ConnectedLeadersRegionedRequest {
    const message = createBaseConnectedLeadersRegionedRequest();
    message.regions = object.regions?.map((e) => e) || [];
    return message;
  },
};

function createBaseConnectedLeadersRegionedResponse(): ConnectedLeadersRegionedResponse {
  return { connectedValidators: {} };
}

export const ConnectedLeadersRegionedResponse: MessageFns<ConnectedLeadersRegionedResponse> = {
  encode(message: ConnectedLeadersRegionedResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    Object.entries(message.connectedValidators).forEach(([key, value]) => {
      ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry.encode(
        { key: key as any, value },
        writer.uint32(10).fork(),
      ).join();
    });
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConnectedLeadersRegionedResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConnectedLeadersRegionedResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          const entry1 = ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.connectedValidators[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConnectedLeadersRegionedResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<ConnectedLeadersRegionedResponse | ConnectedLeadersRegionedResponse[]>
      | Iterable<ConnectedLeadersRegionedResponse | ConnectedLeadersRegionedResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRegionedResponse.encode(p).finish()];
        }
      } else {
        yield* [ConnectedLeadersRegionedResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConnectedLeadersRegionedResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConnectedLeadersRegionedResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRegionedResponse.decode(p)];
        }
      } else {
        yield* [ConnectedLeadersRegionedResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ConnectedLeadersRegionedResponse {
    return {
      connectedValidators: isObject(object.connectedValidators)
        ? Object.entries(object.connectedValidators).reduce<{ [key: string]: ConnectedLeadersResponse }>(
          (acc, [key, value]) => {
            acc[key] = ConnectedLeadersResponse.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
    };
  },

  toJSON(message: ConnectedLeadersRegionedResponse): unknown {
    const obj: any = {};
    if (message.connectedValidators) {
      const entries = Object.entries(message.connectedValidators);
      if (entries.length > 0) {
        obj.connectedValidators = {};
        entries.forEach(([k, v]) => {
          obj.connectedValidators[k] = ConnectedLeadersResponse.toJSON(v);
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConnectedLeadersRegionedResponse>, I>>(
    base?: I,
  ): ConnectedLeadersRegionedResponse {
    return ConnectedLeadersRegionedResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConnectedLeadersRegionedResponse>, I>>(
    object: I,
  ): ConnectedLeadersRegionedResponse {
    const message = createBaseConnectedLeadersRegionedResponse();
    message.connectedValidators = Object.entries(object.connectedValidators ?? {}).reduce<
      { [key: string]: ConnectedLeadersResponse }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = ConnectedLeadersResponse.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseConnectedLeadersRegionedResponse_ConnectedValidatorsEntry(): ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry {
  return { key: "", value: undefined };
}

export const ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry: MessageFns<
  ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry
> = {
  encode(
    message: ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry,
    writer: BinaryWriter = new BinaryWriter(),
  ): BinaryWriter {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      ConnectedLeadersResponse.encode(message.value, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConnectedLeadersRegionedResponse_ConnectedValidatorsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.value = ConnectedLeadersResponse.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<
        | ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry
        | ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry[]
      >
      | Iterable<
        | ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry
        | ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry[]
      >,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry.encode(p).finish()];
        }
      } else {
        yield* [ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry.decode(p)];
        }
      } else {
        yield* [ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? ConnectedLeadersResponse.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = ConnectedLeadersResponse.toJSON(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry>, I>>(
    base?: I,
  ): ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry {
    return ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry>, I>>(
    object: I,
  ): ConnectedLeadersRegionedResponse_ConnectedValidatorsEntry {
    const message = createBaseConnectedLeadersRegionedResponse_ConnectedValidatorsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? ConnectedLeadersResponse.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseGetTipAccountsRequest(): GetTipAccountsRequest {
  return {};
}

export const GetTipAccountsRequest: MessageFns<GetTipAccountsRequest> = {
  encode(_: GetTipAccountsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTipAccountsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTipAccountsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetTipAccountsRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetTipAccountsRequest | GetTipAccountsRequest[]>
      | Iterable<GetTipAccountsRequest | GetTipAccountsRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTipAccountsRequest.encode(p).finish()];
        }
      } else {
        yield* [GetTipAccountsRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetTipAccountsRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetTipAccountsRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTipAccountsRequest.decode(p)];
        }
      } else {
        yield* [GetTipAccountsRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): GetTipAccountsRequest {
    return {};
  },

  toJSON(_: GetTipAccountsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetTipAccountsRequest>, I>>(base?: I): GetTipAccountsRequest {
    return GetTipAccountsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTipAccountsRequest>, I>>(_: I): GetTipAccountsRequest {
    const message = createBaseGetTipAccountsRequest();
    return message;
  },
};

function createBaseGetTipAccountsResponse(): GetTipAccountsResponse {
  return { accounts: [] };
}

export const GetTipAccountsResponse: MessageFns<GetTipAccountsResponse> = {
  encode(message: GetTipAccountsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.accounts) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetTipAccountsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTipAccountsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.accounts.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetTipAccountsResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetTipAccountsResponse | GetTipAccountsResponse[]>
      | Iterable<GetTipAccountsResponse | GetTipAccountsResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTipAccountsResponse.encode(p).finish()];
        }
      } else {
        yield* [GetTipAccountsResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetTipAccountsResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetTipAccountsResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetTipAccountsResponse.decode(p)];
        }
      } else {
        yield* [GetTipAccountsResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetTipAccountsResponse {
    return {
      accounts: globalThis.Array.isArray(object?.accounts) ? object.accounts.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: GetTipAccountsResponse): unknown {
    const obj: any = {};
    if (message.accounts?.length) {
      obj.accounts = message.accounts;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetTipAccountsResponse>, I>>(base?: I): GetTipAccountsResponse {
    return GetTipAccountsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTipAccountsResponse>, I>>(object: I): GetTipAccountsResponse {
    const message = createBaseGetTipAccountsResponse();
    message.accounts = object.accounts?.map((e) => e) || [];
    return message;
  },
};

function createBaseSubscribeBundleResultsRequest(): SubscribeBundleResultsRequest {
  return {};
}

export const SubscribeBundleResultsRequest: MessageFns<SubscribeBundleResultsRequest> = {
  encode(_: SubscribeBundleResultsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): SubscribeBundleResultsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubscribeBundleResultsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<SubscribeBundleResultsRequest, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<SubscribeBundleResultsRequest | SubscribeBundleResultsRequest[]>
      | Iterable<SubscribeBundleResultsRequest | SubscribeBundleResultsRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeBundleResultsRequest.encode(p).finish()];
        }
      } else {
        yield* [SubscribeBundleResultsRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, SubscribeBundleResultsRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<SubscribeBundleResultsRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [SubscribeBundleResultsRequest.decode(p)];
        }
      } else {
        yield* [SubscribeBundleResultsRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): SubscribeBundleResultsRequest {
    return {};
  },

  toJSON(_: SubscribeBundleResultsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<SubscribeBundleResultsRequest>, I>>(base?: I): SubscribeBundleResultsRequest {
    return SubscribeBundleResultsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeBundleResultsRequest>, I>>(_: I): SubscribeBundleResultsRequest {
    const message = createBaseSubscribeBundleResultsRequest();
    return message;
  },
};

function createBaseGetRegionsRequest(): GetRegionsRequest {
  return {};
}

export const GetRegionsRequest: MessageFns<GetRegionsRequest> = {
  encode(_: GetRegionsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRegionsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRegionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetRegionsRequest, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<GetRegionsRequest | GetRegionsRequest[]> | Iterable<GetRegionsRequest | GetRegionsRequest[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetRegionsRequest.encode(p).finish()];
        }
      } else {
        yield* [GetRegionsRequest.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetRegionsRequest>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetRegionsRequest> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetRegionsRequest.decode(p)];
        }
      } else {
        yield* [GetRegionsRequest.decode(pkt as any)];
      }
    }
  },

  fromJSON(_: any): GetRegionsRequest {
    return {};
  },

  toJSON(_: GetRegionsRequest): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRegionsRequest>, I>>(base?: I): GetRegionsRequest {
    return GetRegionsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRegionsRequest>, I>>(_: I): GetRegionsRequest {
    const message = createBaseGetRegionsRequest();
    return message;
  },
};

function createBaseGetRegionsResponse(): GetRegionsResponse {
  return { currentRegion: "", availableRegions: [] };
}

export const GetRegionsResponse: MessageFns<GetRegionsResponse> = {
  encode(message: GetRegionsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentRegion !== "") {
      writer.uint32(10).string(message.currentRegion);
    }
    for (const v of message.availableRegions) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRegionsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRegionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.currentRegion = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.availableRegions.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<GetRegionsResponse, Uint8Array>
  async *encodeTransform(
    source:
      | AsyncIterable<GetRegionsResponse | GetRegionsResponse[]>
      | Iterable<GetRegionsResponse | GetRegionsResponse[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetRegionsResponse.encode(p).finish()];
        }
      } else {
        yield* [GetRegionsResponse.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, GetRegionsResponse>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<GetRegionsResponse> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [GetRegionsResponse.decode(p)];
        }
      } else {
        yield* [GetRegionsResponse.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): GetRegionsResponse {
    return {
      currentRegion: isSet(object.currentRegion) ? globalThis.String(object.currentRegion) : "",
      availableRegions: globalThis.Array.isArray(object?.availableRegions)
        ? object.availableRegions.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: GetRegionsResponse): unknown {
    const obj: any = {};
    if (message.currentRegion !== "") {
      obj.currentRegion = message.currentRegion;
    }
    if (message.availableRegions?.length) {
      obj.availableRegions = message.availableRegions;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRegionsResponse>, I>>(base?: I): GetRegionsResponse {
    return GetRegionsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRegionsResponse>, I>>(object: I): GetRegionsResponse {
    const message = createBaseGetRegionsResponse();
    message.currentRegion = object.currentRegion ?? "";
    message.availableRegions = object.availableRegions?.map((e) => e) || [];
    return message;
  },
};

export type SearcherServiceService = typeof SearcherServiceService;
export const SearcherServiceService = {
  /**
   * Searchers can invoke this endpoint to subscribe to their respective bundle results.
   * A success result would indicate the bundle won its state auction and was submitted to the validator.
   */
  subscribeBundleResults: {
    path: "/searcher.SearcherService/SubscribeBundleResults",
    requestStream: false,
    responseStream: true,
    requestSerialize: (value: SubscribeBundleResultsRequest) =>
      Buffer.from(SubscribeBundleResultsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SubscribeBundleResultsRequest.decode(value),
    responseSerialize: (value: BundleResult) => Buffer.from(BundleResult.encode(value).finish()),
    responseDeserialize: (value: Buffer) => BundleResult.decode(value),
  },
  sendBundle: {
    path: "/searcher.SearcherService/SendBundle",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: SendBundleRequest) => Buffer.from(SendBundleRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => SendBundleRequest.decode(value),
    responseSerialize: (value: SendBundleResponse) => Buffer.from(SendBundleResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => SendBundleResponse.decode(value),
  },
  /** Returns the next scheduled leader connected to the block engine. */
  getNextScheduledLeader: {
    path: "/searcher.SearcherService/GetNextScheduledLeader",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: NextScheduledLeaderRequest) =>
      Buffer.from(NextScheduledLeaderRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => NextScheduledLeaderRequest.decode(value),
    responseSerialize: (value: NextScheduledLeaderResponse) =>
      Buffer.from(NextScheduledLeaderResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => NextScheduledLeaderResponse.decode(value),
  },
  /** Returns leader slots for connected jito validators during the current epoch. Only returns data for this region. */
  getConnectedLeaders: {
    path: "/searcher.SearcherService/GetConnectedLeaders",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ConnectedLeadersRequest) => Buffer.from(ConnectedLeadersRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => ConnectedLeadersRequest.decode(value),
    responseSerialize: (value: ConnectedLeadersResponse) =>
      Buffer.from(ConnectedLeadersResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => ConnectedLeadersResponse.decode(value),
  },
  /** Returns leader slots for connected jito validators during the current epoch. */
  getConnectedLeadersRegioned: {
    path: "/searcher.SearcherService/GetConnectedLeadersRegioned",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ConnectedLeadersRegionedRequest) =>
      Buffer.from(ConnectedLeadersRegionedRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => ConnectedLeadersRegionedRequest.decode(value),
    responseSerialize: (value: ConnectedLeadersRegionedResponse) =>
      Buffer.from(ConnectedLeadersRegionedResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => ConnectedLeadersRegionedResponse.decode(value),
  },
  /** Returns the tip accounts searchers shall transfer funds to for the leader to claim. */
  getTipAccounts: {
    path: "/searcher.SearcherService/GetTipAccounts",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetTipAccountsRequest) => Buffer.from(GetTipAccountsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetTipAccountsRequest.decode(value),
    responseSerialize: (value: GetTipAccountsResponse) => Buffer.from(GetTipAccountsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetTipAccountsResponse.decode(value),
  },
  /** Returns region the client is directly connected to, along with all available regions */
  getRegions: {
    path: "/searcher.SearcherService/GetRegions",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetRegionsRequest) => Buffer.from(GetRegionsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetRegionsRequest.decode(value),
    responseSerialize: (value: GetRegionsResponse) => Buffer.from(GetRegionsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetRegionsResponse.decode(value),
  },
} as const;

export interface SearcherServiceServer extends UntypedServiceImplementation {
  /**
   * Searchers can invoke this endpoint to subscribe to their respective bundle results.
   * A success result would indicate the bundle won its state auction and was submitted to the validator.
   */
  subscribeBundleResults: handleServerStreamingCall<SubscribeBundleResultsRequest, BundleResult>;
  sendBundle: handleUnaryCall<SendBundleRequest, SendBundleResponse>;
  /** Returns the next scheduled leader connected to the block engine. */
  getNextScheduledLeader: handleUnaryCall<NextScheduledLeaderRequest, NextScheduledLeaderResponse>;
  /** Returns leader slots for connected jito validators during the current epoch. Only returns data for this region. */
  getConnectedLeaders: handleUnaryCall<ConnectedLeadersRequest, ConnectedLeadersResponse>;
  /** Returns leader slots for connected jito validators during the current epoch. */
  getConnectedLeadersRegioned: handleUnaryCall<ConnectedLeadersRegionedRequest, ConnectedLeadersRegionedResponse>;
  /** Returns the tip accounts searchers shall transfer funds to for the leader to claim. */
  getTipAccounts: handleUnaryCall<GetTipAccountsRequest, GetTipAccountsResponse>;
  /** Returns region the client is directly connected to, along with all available regions */
  getRegions: handleUnaryCall<GetRegionsRequest, GetRegionsResponse>;
}

export interface SearcherServiceClient extends Client {
  /**
   * Searchers can invoke this endpoint to subscribe to their respective bundle results.
   * A success result would indicate the bundle won its state auction and was submitted to the validator.
   */
  subscribeBundleResults(
    request: SubscribeBundleResultsRequest,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<BundleResult>;
  subscribeBundleResults(
    request: SubscribeBundleResultsRequest,
    metadata?: Metadata,
    options?: Partial<CallOptions>,
  ): ClientReadableStream<BundleResult>;
  sendBundle(
    request: SendBundleRequest,
    callback: (error: ServiceError | null, response: SendBundleResponse) => void,
  ): ClientUnaryCall;
  sendBundle(
    request: SendBundleRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: SendBundleResponse) => void,
  ): ClientUnaryCall;
  sendBundle(
    request: SendBundleRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: SendBundleResponse) => void,
  ): ClientUnaryCall;
  /** Returns the next scheduled leader connected to the block engine. */
  getNextScheduledLeader(
    request: NextScheduledLeaderRequest,
    callback: (error: ServiceError | null, response: NextScheduledLeaderResponse) => void,
  ): ClientUnaryCall;
  getNextScheduledLeader(
    request: NextScheduledLeaderRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: NextScheduledLeaderResponse) => void,
  ): ClientUnaryCall;
  getNextScheduledLeader(
    request: NextScheduledLeaderRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: NextScheduledLeaderResponse) => void,
  ): ClientUnaryCall;
  /** Returns leader slots for connected jito validators during the current epoch. Only returns data for this region. */
  getConnectedLeaders(
    request: ConnectedLeadersRequest,
    callback: (error: ServiceError | null, response: ConnectedLeadersResponse) => void,
  ): ClientUnaryCall;
  getConnectedLeaders(
    request: ConnectedLeadersRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: ConnectedLeadersResponse) => void,
  ): ClientUnaryCall;
  getConnectedLeaders(
    request: ConnectedLeadersRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: ConnectedLeadersResponse) => void,
  ): ClientUnaryCall;
  /** Returns leader slots for connected jito validators during the current epoch. */
  getConnectedLeadersRegioned(
    request: ConnectedLeadersRegionedRequest,
    callback: (error: ServiceError | null, response: ConnectedLeadersRegionedResponse) => void,
  ): ClientUnaryCall;
  getConnectedLeadersRegioned(
    request: ConnectedLeadersRegionedRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: ConnectedLeadersRegionedResponse) => void,
  ): ClientUnaryCall;
  getConnectedLeadersRegioned(
    request: ConnectedLeadersRegionedRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: ConnectedLeadersRegionedResponse) => void,
  ): ClientUnaryCall;
  /** Returns the tip accounts searchers shall transfer funds to for the leader to claim. */
  getTipAccounts(
    request: GetTipAccountsRequest,
    callback: (error: ServiceError | null, response: GetTipAccountsResponse) => void,
  ): ClientUnaryCall;
  getTipAccounts(
    request: GetTipAccountsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetTipAccountsResponse) => void,
  ): ClientUnaryCall;
  getTipAccounts(
    request: GetTipAccountsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetTipAccountsResponse) => void,
  ): ClientUnaryCall;
  /** Returns region the client is directly connected to, along with all available regions */
  getRegions(
    request: GetRegionsRequest,
    callback: (error: ServiceError | null, response: GetRegionsResponse) => void,
  ): ClientUnaryCall;
  getRegions(
    request: GetRegionsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetRegionsResponse) => void,
  ): ClientUnaryCall;
  getRegions(
    request: GetRegionsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetRegionsResponse) => void,
  ): ClientUnaryCall;
}

export const SearcherServiceClient = makeGenericClientConstructor(
  SearcherServiceService,
  "searcher.SearcherService",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): SearcherServiceClient;
  service: typeof SearcherServiceService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
