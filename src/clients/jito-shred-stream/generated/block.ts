// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.1
//   protoc               v5.29.3
// source: block.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Header } from "./shared";

/** Condensed block helpful for getting data around efficiently internal to our system. */
export interface CondensedBlock {
  header: Header | undefined;
  previousBlockhash: string;
  blockhash: string;
  parentSlot: bigint;
  versionedTransactions: Buffer[];
  slot: bigint;
  commitment: string;
}

function createBaseCondensedBlock(): CondensedBlock {
  return {
    header: undefined,
    previousBlockhash: "",
    blockhash: "",
    parentSlot: 0n,
    versionedTransactions: [],
    slot: 0n,
    commitment: "",
  };
}

export const CondensedBlock: MessageFns<CondensedBlock> = {
  encode(message: CondensedBlock, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.header !== undefined) {
      Header.encode(message.header, writer.uint32(10).fork()).join();
    }
    if (message.previousBlockhash !== "") {
      writer.uint32(18).string(message.previousBlockhash);
    }
    if (message.blockhash !== "") {
      writer.uint32(26).string(message.blockhash);
    }
    if (message.parentSlot !== 0n) {
      if (BigInt.asUintN(64, message.parentSlot) !== message.parentSlot) {
        throw new globalThis.Error("value provided for field message.parentSlot of type uint64 too large");
      }
      writer.uint32(32).uint64(message.parentSlot);
    }
    for (const v of message.versionedTransactions) {
      writer.uint32(42).bytes(v!);
    }
    if (message.slot !== 0n) {
      if (BigInt.asUintN(64, message.slot) !== message.slot) {
        throw new globalThis.Error("value provided for field message.slot of type uint64 too large");
      }
      writer.uint32(48).uint64(message.slot);
    }
    if (message.commitment !== "") {
      writer.uint32(58).string(message.commitment);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CondensedBlock {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCondensedBlock();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.header = Header.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.previousBlockhash = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.blockhash = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.parentSlot = reader.uint64() as bigint;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.versionedTransactions.push(Buffer.from(reader.bytes()));
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.slot = reader.uint64() as bigint;
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.commitment = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  // encodeTransform encodes a source of message objects.
  // Transform<CondensedBlock, Uint8Array>
  async *encodeTransform(
    source: AsyncIterable<CondensedBlock | CondensedBlock[]> | Iterable<CondensedBlock | CondensedBlock[]>,
  ): AsyncIterable<Uint8Array> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CondensedBlock.encode(p).finish()];
        }
      } else {
        yield* [CondensedBlock.encode(pkt as any).finish()];
      }
    }
  },

  // decodeTransform decodes a source of encoded messages.
  // Transform<Uint8Array, CondensedBlock>
  async *decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<CondensedBlock> {
    for await (const pkt of source) {
      if (globalThis.Array.isArray(pkt)) {
        for (const p of (pkt as any)) {
          yield* [CondensedBlock.decode(p)];
        }
      } else {
        yield* [CondensedBlock.decode(pkt as any)];
      }
    }
  },

  fromJSON(object: any): CondensedBlock {
    return {
      header: isSet(object.header) ? Header.fromJSON(object.header) : undefined,
      previousBlockhash: isSet(object.previousBlockhash) ? globalThis.String(object.previousBlockhash) : "",
      blockhash: isSet(object.blockhash) ? globalThis.String(object.blockhash) : "",
      parentSlot: isSet(object.parentSlot) ? BigInt(object.parentSlot) : 0n,
      versionedTransactions: globalThis.Array.isArray(object?.versionedTransactions)
        ? object.versionedTransactions.map((e: any) => Buffer.from(bytesFromBase64(e)))
        : [],
      slot: isSet(object.slot) ? BigInt(object.slot) : 0n,
      commitment: isSet(object.commitment) ? globalThis.String(object.commitment) : "",
    };
  },

  toJSON(message: CondensedBlock): unknown {
    const obj: any = {};
    if (message.header !== undefined) {
      obj.header = Header.toJSON(message.header);
    }
    if (message.previousBlockhash !== "") {
      obj.previousBlockhash = message.previousBlockhash;
    }
    if (message.blockhash !== "") {
      obj.blockhash = message.blockhash;
    }
    if (message.parentSlot !== 0n) {
      obj.parentSlot = message.parentSlot.toString();
    }
    if (message.versionedTransactions?.length) {
      obj.versionedTransactions = message.versionedTransactions.map((e) => base64FromBytes(e));
    }
    if (message.slot !== 0n) {
      obj.slot = message.slot.toString();
    }
    if (message.commitment !== "") {
      obj.commitment = message.commitment;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CondensedBlock>, I>>(base?: I): CondensedBlock {
    return CondensedBlock.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CondensedBlock>, I>>(object: I): CondensedBlock {
    const message = createBaseCondensedBlock();
    message.header = (object.header !== undefined && object.header !== null)
      ? Header.fromPartial(object.header)
      : undefined;
    message.previousBlockhash = object.previousBlockhash ?? "";
    message.blockhash = object.blockhash ?? "";
    message.parentSlot = object.parentSlot ?? 0n;
    message.versionedTransactions = object.versionedTransactions?.map((e) => e) || [];
    message.slot = object.slot ?? 0n;
    message.commitment = object.commitment ?? "";
    return message;
  },
};

function bytesFromBase64(b64: string): Uint8Array {
  return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}

function base64FromBytes(arr: Uint8Array): string {
  return globalThis.Buffer.from(arr).toString("base64");
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  encodeTransform(source: AsyncIterable<T | T[]> | Iterable<T | T[]>): AsyncIterable<Uint8Array>;
  decodeTransform(
    source: AsyncIterable<Uint8Array | Uint8Array[]> | Iterable<Uint8Array | Uint8Array[]>,
  ): AsyncIterable<T>;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
